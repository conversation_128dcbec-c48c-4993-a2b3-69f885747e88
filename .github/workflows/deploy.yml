name: 'Backend Checks'

on:
  push:
    branches: ['dev']

jobs:
  publish:
    runs-on: ubuntu-latest
    environment:
      name: ${{ github.ref_name }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Build & Deploy
        uses: appleboy/ssh-action@v1.0.3
        env:
          REPO_NAME: ${{ github.repository }}
          BRANCH_NAME: ${{ github.ref_name }}
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.USER_NAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: ${{ secrets.PORT }}
          envs: REPO_NAME,BRANCH_NAME
          script: |
            sudo chmod 777 /var/run/docker.sock
            echo "REPO_NAME: $REPO_NAME"
            echo "branch_name: $BRANCH_NAME"
            echo "GITHUB_SHA: $GITHUB_SHA"
            GITHUB_SHA="${{ github.sha }}"
            REPOS_NAME=$(echo "$REPO_NAME" | cut -d'/' -f2)
            CONTAINER_NAME="${REPOS_NAME}"
            echo "REPOS_NAME: $REPOS_NAME"
            ENV="${BRANCH_NAME}"       
            if [[ ! -d "$REPOS_NAME" ]]; then
              <NAME_EMAIL>:Rapid-Innovation/football-fantasy-be.git -b "$BRANCH_NAME" "$REPOS_NAME"
              cd "$REPOS_NAME"
            else
              cd "$REPOS_NAME"
              git pull origin "$BRANCH_NAME"
            fi
            GIT_HASH=$(git rev-parse --short "$GITHUB_SHA")
            echo "GIT_HASH=$GIT_HASH"
            IMAGE_NAME="$(echo "${REPOS_NAME}:${GIT_HASH}" | tr '[:upper:]' '[:lower:]')"
            if [ $? -eq 0 ]; then
              docker build --build-arg ENV="$ENV" -t "$IMAGE_NAME" .
              if [ $? -eq 0 ]; then
                if docker ps -a --format '{{.Names}}' | grep -Eq "^$CONTAINER_NAME$"; then
                  echo "Stopping existing container: $CONTAINER_NAME"
                  docker stop "$CONTAINER_NAME" || true
                  echo "Removing existing container: $CONTAINER_NAME"
                  docker rm "$CONTAINER_NAME" || true
                else
                  echo "docker container stop failed or container not exists"
                fi  
              else
                echo "Docker build failed."
                exit 1
              fi
            else
              echo "Git operation failed."
              exit 1
            fi


            docker run -d --restart=always --network=football-fantasy -p 3000:3000 --name "$CONTAINER_NAME" "$IMAGE_NAME"
            docker image prune -a -f || true