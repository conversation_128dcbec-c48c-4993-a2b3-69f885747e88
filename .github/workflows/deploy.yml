name: 'Backend Checks'

on:
  push:
    branches: ['dev']
  pull_request:
    branches: ['dev']

jobs:
  sonarqube-analysis:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Shallow clones should be disabled for better analysis

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run tests with coverage
        run: npm run test:cov

      - name: SonarQube Scan
        uses:  SonarSource/sonarqube-scan-action@v5
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
        with:
          args: >
            -Dsonar.projectKey=${{ secrets.SONAR_PROJECT_KEY }}
            -Dsonar.test.inclusions=**/*.spec.ts,**/*.test.ts
            -Dsonar.exclusions=**/node_modules/**,**/dist/**,**/coverage/**,**/*.spec.ts,**/*.test.ts
            -Dsonar.sourceEncoding=UTF-8
            -Dsonar.javascript.lcov.reportPaths=./coverage/lcov.info
            -Dsonar.coverage.exclusions=**/*.spec.ts,**/*.test.ts,**/main.ts,**/test/**
            -Dsonar.typescript.tsconfigPath=tsconfig.json
            -Dsonar.qualitygate.wait=true

      # - name: SonarQube Quality Gate Check
      #   uses: sonarqube/sonarqube-quality-gate-action@master
      #   timeout-minutes: 5
      #   env:
      #     SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      #     SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

  publish:
    runs-on: ubuntu-latest
    needs: sonarqube-analysis
    if: github.event_name == 'push'  # Only deploy on push, not on PR
    environment:
      name: ${{ github.ref_name }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Build & Deploy
        uses: appleboy/ssh-action@v1.0.3
        env:
          REPO_NAME: ${{ github.repository }}
          BRANCH_NAME: ${{ github.ref_name }}
          ENVS: ${{ secrets.ENVS }} 
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.USER_NAME }}
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          port: ${{ secrets.PORT }}
          envs: REPO_NAME,BRANCH_NAME,ENVS
          script: |
            sudo chmod 777 /var/run/docker.sock
            echo "REPO_NAME: $REPO_NAME"
            echo "branch_name: $BRANCH_NAME"
            echo "GITHUB_SHA: $GITHUB_SHA"
            GITHUB_SHA="${{ github.sha }}"
            REPOS_NAME=$(echo "$REPO_NAME" | cut -d'/' -f2)
            CONTAINER_NAME="${REPOS_NAME}"
            echo "REPOS_NAME: $REPOS_NAME"
            echo "ENVS: $ENVS"
            ENV="${BRANCH_NAME}"
            
            
            if [[ ! -d "$REPOS_NAME" ]]; then
              <NAME_EMAIL>:Rapid-Innovation/football-fantasy-be.git -b "$BRANCH_NAME" "$REPOS_NAME"
              cd "$REPOS_NAME"
            else
              cd "$REPOS_NAME"
              git pull origin "$BRANCH_NAME"
            fi
            GIT_HASH=$(git rev-parse --short "$GITHUB_SHA")
            echo "GIT_HASH=$GIT_HASH"
            ENV_FILE=".env.${ENV}"
      
            echo "${ENVS}" >> "$ENV_FILE"
            echo "Contents of ${ENV_FILE}:"
            cat "$ENV_FILE"
            
            IMAGE_NAME="$(echo "${REPOS_NAME}:${GIT_HASH}" | tr '[:upper:]' '[:lower:]')"
            if [ $? -eq 0 ]; then
              docker build --build-arg ENV="$ENV" -t "$IMAGE_NAME" .
              if [ $? -eq 0 ]; then
                if docker ps -a --format '{{.Names}}' | grep -Eq "^$CONTAINER_NAME$"; then
                  echo "Stopping existing container: $CONTAINER_NAME"
                  docker stop "$CONTAINER_NAME" || true
                  echo "Removing existing container: $CONTAINER_NAME"
                  docker rm "$CONTAINER_NAME" || true
                else
                  echo "docker container stop failed or container not exists"
                fi  
              else
                echo "Docker build failed."
                exit 1
              fi
            else
              echo "Git operation failed."
              exit 1
            fi


            docker run -d --label io.portainer.accesscontrol.teams=football-fantasy --restart=always --network=football-fantasy -p 3000:3000 --name "$CONTAINER_NAME" "$IMAGE_NAME"
            docker image prune -a -f || true
