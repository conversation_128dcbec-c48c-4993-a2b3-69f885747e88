# League Manager Backend

A robust backend system built with NestJS for managing sports leagues with blockchain integration using Solana.

## Features

- User authentication and authorization
- League management system
- Premier League integration
- Solana blockchain integration
- Wallet management
- RESTful API with Swagger documentation
- PostgreSQL database integration
- TypeORM for database operations

## Prerequisites

- Node.js (v18 or higher)
- PostgreSQL
- Solana CLI tools (for blockchain integration)

## Installation

1. Clone the repository:
```bash
git clone [repository-url]
cd leauge_manager_back
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
Create a `.env` file in the root directory with the following variables:
```env
DATABASE_URL=postgresql://username:password@localhost:5432/league_manager
JWT_SECRET=your_jwt_secret
SOLANA_RPC_URL=your_solana_rpc_url
```

## Running the Application

### Development
```bash
npm run start:dev
```

### Production
```bash
npm run build
npm run start:prod
```

### Testing
```bash
# Unit tests
npm run test

# e2e tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## API Documentation

Once the application is running, you can access the Swagger API documentation at:
```
http://localhost:3000/api
```

## Project Structure

```
src/
├── auth/           # Authentication module
├── config/         # Configuration files
├── leagues/        # League management module
├── migrations/     # Database migrations
├── premier-league/ # Premier League integration
├── solana/         # Solana blockchain integration
├── users/          # User management module
└── wallet/         # Wallet management module
```

## Available Scripts

- `npm run build` - Build the application
- `npm run format` - Format code using Prettier
- `npm run start` - Start the application
- `npm run start:dev` - Start the application in development mode
- `npm run start:debug` - Start the application in debug mode
- `npm run start:prod` - Start the application in production mode
- `npm run lint` - Lint the code
- `npm run test` - Run unit tests
- `npm run test:watch` - Run unit tests in watch mode
- `npm run test:cov` - Run unit tests with coverage
- `npm run test:e2e` - Run e2e tests

## Code Quality & Analysis

### SonarQube Integration

This project includes SonarQube analysis for code quality, security, and coverage monitoring.

#### Setup Requirements

1. **SonarQube Server**: Ensure you have access to a SonarQube server
2. **GitHub Secrets**: Configure the following secrets in your GitHub repository:
   - `SONAR_TOKEN`: Your SonarQube authentication token
   - `SONAR_HOST_URL`: Your SonarQube server URL (e.g., `https://sonarcloud.io`)
   - `SONAR_PROJECT_KEY`: Your SonarQube project key (e.g., `football-fantasy-be`)

#### Configuration

The project includes:
- SonarQube configuration embedded in GitHub Actions workflow
- Automated analysis on push/PR to `dev` branch
- Code coverage integration with Jest

#### Running Analysis Locally

```bash
# Install SonarQube Scanner
npm install -g sonarqube-scanner

# Run tests with coverage
npm run test:cov

# Run SonarQube analysis
sonar-scanner
```

#### Quality Gates

The CI/CD pipeline includes quality gate checks that must pass before deployment:
- Code coverage thresholds
- Security vulnerability checks
- Code smell detection
- Duplication analysis

## Technologies Used

- NestJS - Progressive Node.js framework
- TypeScript - Programming language
- PostgreSQL - Database
- TypeORM - Object-Relational Mapping
- Solana Web3.js - Blockchain integration
- JWT - Authentication
- Swagger - API documentation
- Jest - Testing framework
- SonarQube - Code quality analysis

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is private and unlicensed.

## Support

For support, please contact the development team.
