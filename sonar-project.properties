# SonarQube Configuration for Football Fantasy Backend

# Project identification
sonar.projectKey=football-fantasy-be
sonar.projectName=Football Fantasy Backend
sonar.projectVersion=1.0

# Source code configuration
sonar.sources=src
sonar.tests=src
sonar.test.inclusions=**/*.spec.ts,**/*.test.ts
sonar.exclusions=**/node_modules/**,**/dist/**,**/coverage/**,**/*.spec.ts,**/*.test.ts

# Language and encoding
sonar.sourceEncoding=UTF-8
sonar.typescript.node=node

# Coverage configuration
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.coverage.exclusions=**/*.spec.ts,**/*.test.ts,**/main.ts,**/test/**

# TypeScript specific settings
sonar.typescript.tsconfigPath=tsconfig.json

# Code analysis settings
sonar.qualitygate.wait=true

# Additional exclusions for generated files
sonar.exclusions=**/node_modules/**,**/dist/**,**/coverage/**,**/*.d.ts
