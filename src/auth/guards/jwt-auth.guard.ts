import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  private readonly logger = new Logger(JwtAuthGuard.name);

  handleRequest(err: any, user: any, info: any) {
    // Log the full error details
    if (err) {
      this.logger.error(`Authentication error: ${JSON.stringify(err)}`);
    }

    // Log the info object if it exists
    if (info) {
      this.logger.debug(`Authentication info: ${JSON.stringify(info)}`);
    }

    // Log the user object if it exists
    // if (user) {
    //   this.logger.debug(`Authenticated user: ${JSON.stringify(user)}`);
    // }

    // If there's an error or no user, throw an UnauthorizedException
    if (err || !user) {
      const errorMessage = err?.message || 'Unauthorized access';
      this.logger.error(`Authentication failed: ${errorMessage}`);
      throw new UnauthorizedException(errorMessage);
    }

    return user;
  }
}
