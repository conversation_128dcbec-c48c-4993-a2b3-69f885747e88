import { Injectable, Logger, ConflictException, InternalServerErrorException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { SignupDto } from './dto/signup.dto';
import { argon2hash, argon2verify } from '../utils/hashes/argon2';
import { JwtPayload } from './interfaces/jwt-payload.interface';
import { InjectLogger } from '../shared/decorators/logger.decorator';
import { Role } from '../users/enums/role.enum';
import { LoginUserDto } from './dto/login.dto';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    @InjectLogger() private readonly logger: Logger,
  ) {}

  async signup(signupDto: SignupDto) {
    console.log(this.configService.get<string>('JWT_SECRET'));
    const { email, password, firstName, lastName } = signupDto;

    const hashedPassword = await argon2hash(password);

    try {
      let user = this.userRepository.create({
        email,
        password: hashedPassword,
        firstName,
        lastName,
        role: Role.USER,
      });

      user = await this.userRepository.save(user);
      const token: string = this.signToken(user);

      return { user, token };
    } catch (err) {
      this.logger.error(err.message, err.stack, AuthService.name);
      if (err.code === '23505') throw new ConflictException('Email already exists');
      else throw new InternalServerErrorException(err.message);
    }
  }

  async loginPassport(loginUserDto: LoginUserDto) {
    const { email, password } = loginUserDto;

    this.logger.log('Searching User with provided email', AuthService.name);
    const user = await this.userRepository.findOne({ where: { email } });

    this.logger.log('Verifying User', AuthService.name);
    if (user && (await argon2verify(user.password, password))) {
      return user;
    }

    return null;
  }

  /**
   * used for signing a JWT token with user id as payload.
   * @param user http request object containing user details provided by google.
   * @returns signed token which is used for authentication.
   */
  signToken(user: User): string {
    const payload: JwtPayload = { id: user.id };
    this.logger.log('Signing token', AuthService.name);

    return this.jwtService.sign(payload);
  }
}
