import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiCreatedResponse,
  ApiUnauthorizedResponse,
  ApiOkResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { SignupDto } from './dto/signup.dto';
import { LoginUserDto } from './dto/login.dto';
import { UserResponseDto } from './dto/auth-response.dto';
import { Role } from '../users/enums/role.enum';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { TransformInterceptor } from '../shared/interceptors/transform.interceptor';
import { Request } from 'express';
import { AuthenticatedRequest } from '../shared/interface/authenticated-request';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('signup')
  @ApiOperation({ summary: 'Register a new user', description: 'Register a new user' })
  @ApiCreatedResponse({ description: 'The user has been successfully created', type: UserResponseDto })
  @ApiBadRequestResponse({ description: 'Bad request' })
  async signUp(@Body() signupDto: SignupDto) {
    const { user, token } = await this.authService.signup(signupDto);

    return {
      status: 'success',
      data: { user, token },
    };
  }

  /**
   * Post API - "/login" - used for user login and get authentication token to access other protected APIs.
   * it requires the LoginUserDto object in request body.
   * @param req HTTP request object containing user information.
   * @returns newly logged in user object, token for authentication and response status.
   * @throws UnauthorizedException with message in case user is not logged in.
   */
  @Post('login')
  @UseGuards(LocalAuthGuard)
  @UseInterceptors(TransformInterceptor)
  @ApiOperation({
    description: 'Api to login already registered user.',
    summary: 'Api to login already registered user.',
  })
  @ApiOkResponse({
    description: 'Login successful',
    type: UserResponseDto,
  })
  @ApiUnauthorizedResponse({ description: 'Invalid credentials' })
  @ApiBody({ required: true, type: LoginUserDto })
  async loginPassportLocal(@Req() req: AuthenticatedRequest) {
    const user = req.user;

    const token = this.authService.signToken(user);

    return { status: 'success', data: { user, token } };
  }
}
