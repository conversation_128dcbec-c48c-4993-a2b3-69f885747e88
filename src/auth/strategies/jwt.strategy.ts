import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { Role } from '../../users/entities/user.entity';

interface JwtPayload {
  sub: number;
  email: string;
  role: Role;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor() {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: process.env.JWT_SECRET || 'league-manager-secret-key-2024', // Use environment variable in production
    });
    this.logger.log('JwtStrategy initialized');
  }

  async validate(payload: JwtPayload) {
    try {
      this.logger.debug(`Validating JWT payload: ${JSON.stringify(payload)}`);

      if (!payload.sub || !payload.email || !payload.role) {
        this.logger.error('Invalid JWT payload structure');
        throw new UnauthorizedException('Invalid token payload');
      }

      const user = { 
        id: payload.sub,
        email: payload.email,
        role: payload.role 
      };

      this.logger.debug(`Successfully validated user: ${JSON.stringify(user)}`);
      return user;
    } catch (error) {
      this.logger.error(`JWT validation error: ${error.message}`);
      throw new UnauthorizedException('Invalid token');
    }
  }
} 