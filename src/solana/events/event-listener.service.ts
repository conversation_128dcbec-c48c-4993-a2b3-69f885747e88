import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { Connection, PublicKey } from '@solana/web3.js';
import { BorshCoder } from '@coral-xyz/anchor';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Idl } from '@coral-xyz/anchor';
import * as fs from 'fs';
import * as path from 'path';
import { SmartContractService } from '../services/smart-contract.service';
import { SolanaService } from '../services/solana.service';
import { LeagueStatus, LeagueType } from '../../leagues/entities/league.entity';
import { v4 as uuidv4 } from 'uuid';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserLeagueData } from '../../leagues/entities/user-league-data.entity';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class EventListenerService implements OnModuleInit, OnModuleDestroy {
  private readonly connection: Connection;
  private readonly programId: PublicKey;
  private readonly coder: BorshCoder;
  private subscriptionId: number | null = null;

  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly smartContractService: SmartContractService,
    private readonly solanaService: SolanaService,
    private readonly configService: ConfigService,
    @InjectRepository(UserLeagueData)
    private readonly userLeagueDataRepository: Repository<UserLeagueData>
  ) {
    // Get connection from SolanaService
    this.connection = this.solanaService.getConnection();
    
    // Get program ID from environment
    const programId = this.configService.get<string>('SOLANA_PROGRAM_ID');
    if (!programId) {
      throw new Error('SOLANA_PROGRAM_ID environment variable is not set');
    }
    this.programId = new PublicKey(programId);

    // Load and initialize IDL
    const idlPath = path.join(process.cwd(), 'src', 'solana', 'contracts', 'idl.json');
    if (!fs.existsSync(idlPath)) {
      throw new Error(`IDL file not found at path: ${idlPath}`);
    }
    const idl = JSON.parse(fs.readFileSync(idlPath, 'utf8')) as Idl;
    this.coder = new BorshCoder(idl);
  }

  async onModuleInit() {
    try {
      // Test the connection
      const version = await this.connection.getVersion();
      console.log('Connected to Solana node version:', version);
      
      await this.startListening();
    } catch (error) {
      console.error('Failed to initialize event listener:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    await this.stopListening();
  }

  private async startListening() {
    try {
      if (!this.programId) {
        throw new Error('Program ID is not initialized');
      }      
      this.subscriptionId = this.connection.onLogs(
        this.programId,
        async (logs, context) => {
          try {
            console.log('Received logs:', {
              signature: logs.signature,
              logs: logs.logs,
              err: logs.err
            });

            if (logs.err) {
              console.error('Error in transaction:', logs.err);
              return;
            }

            // Process each log entry
            for (const log of logs.logs) {
              try {              
                // Check for register_participant instruction
                if (log.includes('Program log: Instruction: RegisterParticipant')) {
                  await this.handleRegisterParticipant(logs);
                }
                // Check if this is a create_league event
                if (log.includes('Program log: Instruction: CreateLeague')) {
                  await this.handleCreateLeague(logs);
                }
              } catch (error) {
                console.error('Error processing log:', error);
              }
            }
          } catch (error) {
            console.error('Error processing transaction logs:', error);
          }
        },
        'confirmed',
      );

      console.log('Started listening for program events');
    } catch (error) {
      console.error('Error starting event listener:', error);
      throw error;
    }
  }

  private async handleRegisterParticipant(logs: any) {
    console.log('Found RegisterParticipant instruction');
    const signature = logs.signature;
    const tx = await this.connection.getTransaction(signature, {
      maxSupportedTransactionVersion: 0,
    });

    if (!tx) {
      console.log('Transaction not found for signature:', signature);
      return;
    }

    const message = tx.transaction.message;
    const accountKeys = message.getAccountKeys();

    for (const instruction of message.compiledInstructions) {
      const programId = accountKeys.get(instruction.programIdIndex);
      if (!programId?.equals(this.programId)) continue;

      const data = instruction.data;
      const discriminator = Buffer.from(data).slice(0, 8);

      if (this.isRegisterParticipantInstruction(discriminator)) {
        const userLeagueDataAccount = accountKeys.get(instruction.accountKeyIndexes[2]);
        if (!userLeagueDataAccount) {
          console.log('User league data account not found');
          continue;
        }

        console.log('Getting account data for userLeagueDataAccount:', userLeagueDataAccount.toString());
        const accountInfo = await this.getAccountInfoWithRetry(userLeagueDataAccount, 3);
        if (!accountInfo || accountInfo.data.length === 0) {
          console.error('Failed to get account data after retries');
          continue;
        }

        const userLeagueData = this.coder.accounts.decode('UserLeagueData', accountInfo.data);
        console.log('Deserialized UserLeagueData:', JSON.stringify(userLeagueData, null, 2));

        await this.processRegisterParticipantEvent(signature, userLeagueDataAccount, userLeagueData);
        this.emitParticipantRegistered(signature, userLeagueDataAccount, userLeagueData);
      }
    }
  }

  private async getAccountInfoWithRetry(account: PublicKey, retries: number): Promise<{ data: Buffer } | null> {
    let accountInfo: { data: Buffer } | null = null;
    while (retries > 0) {
      const info = await this.connection.getAccountInfo(account);
      if (info && info.data.length > 0) {
        accountInfo = info;
        break;
      }
      await new Promise(resolve => setTimeout(resolve, 1000));
      retries--;
    }
    return accountInfo;
  }

  private emitParticipantRegistered(signature: string, userLeagueDataAccount: PublicKey, userLeagueData: any) {
    this.eventEmitter.emit('participant.registered', {
      signature,
      userLeagueDataAccount: userLeagueDataAccount.toString(),
      data: userLeagueData,
      timestamp: new Date().toISOString(),
    });
  }

  private async handleCreateLeague(logs: any) {
    console.log('Found CreateLeague instruction');
    const signature = logs.signature;
    
    // Get the full transaction
    const tx = await this.connection.getTransaction(signature, {
      maxSupportedTransactionVersion: 0,
    });

    if (!tx) {
      console.log('Transaction not found for signature:', signature);
      return;
    }

    const message = tx.transaction.message;
    const accountKeys = message.getAccountKeys();
    
    // Find the league account
    for (const instruction of message.compiledInstructions) {
      const programId = accountKeys.get(instruction.programIdIndex);
      
      if (!programId?.equals(this.programId)) continue;

      const data = instruction.data;
      const discriminator = Buffer.from(data).slice(0, 8);
      
      if (this.isCreateLeagueInstruction(discriminator)) {
        const leagueAccount = accountKeys.get(instruction.accountKeyIndexes[0]);
        
        if (!leagueAccount) {
          console.log('League account not found');
          continue;
        }

        // Get the account data
        const accountInfo = await this.connection.getAccountInfo(leagueAccount);
        if (!accountInfo) {
          console.log('League account info not found');
          continue;
        }

        // Deserialize the account data
        const leagueData = this.coder.accounts.decode('League', accountInfo.data);
        console.log('Deserialized League Account Data:', JSON.stringify(leagueData, null, 2));
        
        await this.processCreateLeagueEvent(signature, leagueAccount, leagueData);
        
        // Emit the event
        this.eventEmitter.emit('league.created', {
          signature,
          leagueAccount: leagueAccount.toString(),
          data: leagueData,
          timestamp: new Date().toISOString(),
        });
      }
    }
  }

  private async stopListening() {
    if (this.subscriptionId !== null) {
      await this.connection.removeOnLogsListener(this.subscriptionId);
      this.subscriptionId = null;
      console.log('Stopped listening for program events');
    }
  }

  private isCreateLeagueInstruction(discriminator: Buffer): boolean {
    const createLeagueDiscriminator = Buffer.from([129, 229, 70, 201, 64, 57, 180, 164]);
    return discriminator.equals(createLeagueDiscriminator);
  }

  private isRegisterParticipantInstruction(discriminator: Buffer): boolean {
    const registerParticipantDiscriminator = Buffer.from([248, 112, 38, 215, 226, 230, 249, 40]);
    return discriminator.equals(registerParticipantDiscriminator);
  }

  private async processCreateLeagueEvent(signature: string, leagueAccount: PublicKey, leagueData: any) {
    try {
      console.log('Raw leagueData:', JSON.stringify(leagueData, null, 2));
      
      // Create league in database with properly deserialized data
      const dbLeague = await this.smartContractService.create({
        // Generate new UUID for id
        id: uuidv4(),
        // league_key is the league account public key as string
        league_key: leagueAccount.toString(),
        // creator is a Pubkey in Rust, stored as string
        creator: leagueData?.creator?.toString() || 'unknown',
        // entry_fee is u64 in Rust, stored as number
        entryFee: Number(leagueData?.entry_fee || 0),
        // participant_limit is u8 in Rust, stored as number
        participantLimit: Number(leagueData?.participant_limit || 0),
        // start_time is i64 in Rust, convert to Date
        startTime: new Date(Number(leagueData?.start_time || 0) * 1000),
        // end_time is i64 in Rust, convert to Date
        endTime: new Date(Number(leagueData?.end_time || 0) * 1000),
        // status is LeagueStatus enum in Rust
        status: LeagueStatus.REGISTRATION,
        // type is LeagueType enum in Rust
        type: leagueData?.league_type?.OneVsOne ? LeagueType.OneVsOne : LeagueType.Global,
        // first_place_percentage is u8 in Rust, stored as number
        firstPlacePercentage: Number(leagueData?.first_place_percentage || 0),
        // second_place_percentage is u8 in Rust, stored as number
        secondPlacePercentage: Number(leagueData?.second_place_percentage || 0),
        // third_place_percentage is u8 in Rust, stored as number
        thirdPlacePercentage: Number(leagueData?.third_place_percentage || 0),
        // current_participants is u8 in Rust, stored as number
        currentParticipants: Number(leagueData?.current_participants || 0),
        // total_prize_pool is u64 in Rust, stored as number
        totalPrizePool: Number(leagueData?.total_prize_pool || 0),
        // description is String in Rust, stored as string
        description: leagueData?.description || 'No description provided',
        // premier_league_id is u64 in Rust, stored as number
        premierLeagueId: Number(leagueData?.premier_leauge_id || 0),

        sesson: Number(leagueData?.sesson || 0),
        week_number: Number(leagueData?.week_number || null),
        globle_league_key: leagueData?.globle_league_key || null,

      });

      console.log('League stored in database:', dbLeague);
    } catch (error) {
      console.error('Error storing league in database:', error);
      console.error('Raw leagueData that caused error:', JSON.stringify(leagueData, null, 2));
    }
  }

  private async processRegisterParticipantEvent(signature: string, userLeagueDataAccount: PublicKey, userLeagueData: any) {
    try {
      console.log('Raw userLeagueData:', JSON.stringify(userLeagueData, null, 2));
      
      // Get the league account from the userLeagueData
      const leagueAccount = new PublicKey(userLeagueData.league_id);
      
      // Get and deserialize the league account data
      const leagueAccountInfo = await this.connection.getAccountInfo(leagueAccount);
      if (!leagueAccountInfo) {
        throw new Error('League account not found');
      }
      
      const leagueData = this.coder.accounts.decode('League', leagueAccountInfo.data);
      console.log('Deserialized League Account Data:', JSON.stringify(leagueData, null, 2));
      
      // Convert blockchain status to our LeagueStatus enum
      let status: LeagueStatus;
      if (leagueData?.status === 0) {
        status = LeagueStatus.REGISTRATION;
      } else if (leagueData?.status === 1) {
        status = LeagueStatus.ACTIVE;
      } else if (leagueData?.status === 2) {
        status = LeagueStatus.COMPLETED;
      } else {
        status = LeagueStatus.REGISTRATION; // Default to REGISTRATION if unknown
      }
      
      // Update the league in database
      await this.smartContractService.updateByLeagueKey(leagueAccount.toString(), {
        currentParticipants: Number(leagueData?.current_participants || 0),
        totalPrizePool: Number(leagueData?.total_prize_pool || 0),
        status: status
      });
      
      // Create UserLeagueData in database with properly deserialized data
      const dbUserLeagueData = await this.userLeagueDataRepository.save({
        id: uuidv4(),
        user: userLeagueData?.user?.toString() || 'unknown',
        league_id: userLeagueData?.league_id?.toString() || 'unknown',
        team_selection_hash: userLeagueData?.team_selection_hash ? Buffer.from(userLeagueData.team_selection_hash) : Buffer.alloc(32),
        last_updated: new Date(Number(userLeagueData?.last_updated || 0) * 1000),
        user_team_id: Number(userLeagueData?.team_id || 0)
      });

      console.log('UserLeagueData stored in database:', dbUserLeagueData);
    } catch (error) {
      console.error('Error storing UserLeagueData in database:', error);
      console.error('Raw userLeagueData that caused error:', JSON.stringify(userLeagueData, null, 2));
    }
  }
} 