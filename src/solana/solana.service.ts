import { Injectable } from '@nestjs/common';
import * as anchor from '@coral-xyz/anchor';
import { Connection, Keypair, PublicKey } from '@solana/web3.js';
import { Program } from '@coral-xyz/anchor';
// import { loadIdl } from './idl.loader';
import * as fs from 'fs';
import * as path from 'path';
const PROGRAM_ID = new PublicKey('88PTaq8vtA5Hwb6UKj6vxPUUTqcctE1SGgpqm4oGaadN');

@Injectable()
export class SolanaService {
  private connection: Connection;
  private program: Program;
  private wallet: Keypair;
  private provider: anchor.AnchorProvider;

  constructor() {
    // Initialize connection to Solana network using environment variable
    const rpcUrl = process.env.SOLANA_RPC_URL || 'https://api.devnet.solana.com';
    this.connection = new Connection(rpcUrl, 'confirmed');
    
    // Initialize wallet from private key
    // Note: In production, you should use environment variables for the private key
    const privateKey = new Uint8Array([ 236,31,171,229,162,197,59,13,101,17,107,108,204,53,110,115,88,33,108,126,82,97,244,179,31,19,18,241,9,132,196,208,251,49,132,88,170,73,152,51,25,208,127,205,36,215,6,181,89,208,126,200,90,185,40,70,181,122,86,77,201,62,209,145]);
    this.wallet = Keypair.fromSecretKey(privateKey);

    // Initialize program with IDL
    this.provider = new anchor.AnchorProvider(
      this.connection,
      new anchor.Wallet(this.wallet),
      { commitment: 'confirmed' }
    );
  const idlPath = path.join(process.cwd(), 'src', 'solana', 'idl.json');

    const idl = JSON.parse(
      fs.readFileSync(
        idlPath,
        "utf8"
      )
    );    
    this.program = new Program(idl, this.provider);
  }

  getProvider(): anchor.AnchorProvider {
    return this.provider;
  }
} 