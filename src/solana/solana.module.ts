import { Modu<PERSON> } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SolanaService } from './solana.service';
import { LeagueService } from './league.service';
import { EventListenerService } from './event-listener.service';
import { League } from '../leagues/entities/league.entity';
import { UserLeagueData } from '../leagues/entities/user-league-data.entity';

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    TypeOrmModule.forFeature([League, UserLeagueData]),
  ],
  providers: [SolanaService, LeagueService, EventListenerService],
  exports: [SolanaService, LeagueService, EventListenerService],
})
export class SolanaModule {} 