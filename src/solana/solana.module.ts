import { Modu<PERSON> } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SolanaService } from './services/solana.service';
import { SmartContractService } from './services/smart-contract.service';
import { EventListenerService } from './events/event-listener.service';
import { League } from '../leagues/entities/league.entity';
import { UserLeagueData } from '../leagues/entities/user-league-data.entity';

@Module({
  imports: [
    EventEmitterModule.forRoot(),
    TypeOrmModule.forFeature([League, UserLeagueData]),
  ],
  providers: [SolanaService, SmartContractService, EventListenerService],
  exports: [SolanaService, SmartContractService, EventListenerService],
})
export class SolanaModule {} 