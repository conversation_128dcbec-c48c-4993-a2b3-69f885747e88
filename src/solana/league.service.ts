import { Injectable } from '@nestjs/common';
import * as anchor from '@coral-xyz/anchor';
import { Connection, Keypair, PublicKey } from '@solana/web3.js';
import { Program } from '@coral-xyz/anchor';
// import { loadIdl } from './idl.loader';
import { SolanaService } from './solana.service';
import * as fs from 'fs';
import * as path from 'path';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { League } from '../leagues/entities/league.entity';
import { LeagueStatus } from '../leagues/entities/league.entity';
import { PremierLeague } from 'src/premier-league/entities/premier-league.entity';
const PROGRAM_ID = new PublicKey('********************************************');

@Injectable()
export class LeagueService {
  private connection: Connection;
  private program: Program;
  private wallet: Keypair;

  constructor(
    private solanaService: SolanaService,
    @InjectRepository(League)
    private leagueRepository: Repository<League>
  ) {
    // Initialize connection to Solana network using environment variable
    const rpcUrl = process.env.SOLANA_RPC_URL || 'https://api.devnet.solana.com';
    this.connection = new Connection(rpcUrl, 'confirmed');
    
    // Initialize wallet from private key
    // Note: In production, you should use environment variables for the private key
    const privateKey = new Uint8Array([ 236,31,171,229,162,197,59,13,101,17,107,108,204,53,110,115,88,33,108,126,82,97,244,179,31,19,18,241,9,132,196,208,251,49,132,88,170,73,152,51,25,208,127,205,36,215,6,181,89,208,126,200,90,185,40,70,181,122,86,77,201,62,209,145]);
    this.wallet = Keypair.fromSecretKey(privateKey);

  const idlPath = path.join(process.cwd(), 'src', 'solana', 'idl.json');
  const idl = JSON.parse(
      fs.readFileSync(
        idlPath,
        "utf8"
      )
    );     
    this.program = new Program(idl, solanaService.getProvider());
  }

  async createLeague(
    entryFee: number,
    participantLimit: number,
    startTime: number,
    endTime: number,
    firstPlacePercentage: number,
    secondPlacePercentage: number,
    thirdPlacePercentage: number,
    description: string,
    premierLeagueId: number
  ) {
    try {
      // Generate a new public key for the league account
      const leagueAccount = Keypair.generate();

      // Find the league manager PDA
      const [leagueManagerPda,bump] = PublicKey.findProgramAddressSync(
        [Buffer.from(anchor.utils.bytes.utf8.encode("league_manager"))],
        this.program.programId
      );

      // Find the league participants PDA
      // Create a PDA for the league participants
    const [leagueParticipantsPda] = PublicKey.findProgramAddressSync(
      [Buffer.from(anchor.utils.bytes.utf8.encode("league_participants")), leagueAccount.publicKey.toBuffer()],
      this.program.programId
    );

      // Call the create_league instruction
       const tx = await this.program.methods
      .createLeague(
        new anchor.BN(entryFee), // entry_fee: 1 SOL
        participantLimit, // participant_limit: 10 participants
        new anchor.BN(startTime), // start_time
        new anchor.BN(endTime), // end_time
        firstPlacePercentage, // first_place_percentage: 50%
        secondPlacePercentage, // second_place_percentage: 30%
        thirdPlacePercentage , // third_place_percentage: 20%,
        description, // description
        { global: {} } ,
        new anchor.BN(premierLeagueId)
      )
      .accounts({
        league: leagueAccount.publicKey,
        leagueManager: leagueManagerPda,
        leagueParticipants: leagueParticipantsPda,
        creator: this.program.provider.publicKey,
        systemProgram: anchor.web3.SystemProgram.programId,
      } as any)
      .signers([leagueAccount])
      .rpc({
            skipPreflight: true,
    });

      return {
        success: true,
        transaction: tx,
        leagueAccount: leagueAccount.publicKey.toString(),
        leagueManager: leagueManagerPda.toString(),
        leagueParticipants: leagueParticipantsPda.toString(),
      };
    } catch (error) {
      console.error('Error creating league:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async create(leagueData: Partial<League>): Promise<League> {
    const league = this.leagueRepository.create(leagueData);
    return await this.leagueRepository.save(league);
  }

  async updateLeagueScores(leagueKey: string, scoreUpdates: { user: string; score: number }[]) {
    try {
      const leaguePublicKey = new PublicKey(leagueKey);

      // Find the league manager PDA
      const [leagueManagerPda] = PublicKey.findProgramAddressSync(
        [Buffer.from(anchor.utils.bytes.utf8.encode("league_manager"))],
        this.program.programId
      );

      // Find the league participants PDA
      const [leagueParticipantsPda] = PublicKey.findProgramAddressSync(
        [Buffer.from(anchor.utils.bytes.utf8.encode("league_participants")), leaguePublicKey.toBuffer()],
        this.program.programId
      );

      // Convert score updates to the format expected by the blockchain
      const newScores = scoreUpdates.map(update => ({
        user: new PublicKey(update.user),
        score: new anchor.BN(update.score)
      }));

      // Call the update_league_scores instruction
      const tx = await this.program.methods
        .updateLeagueScores(newScores)
        .accounts({
          league: leaguePublicKey,
          leagueParticipants: leagueParticipantsPda,
          leagueManager: leagueManagerPda,
          authority: this.program.provider.publicKey,
        } as any)
        .rpc({
          skipPreflight: true,
        });

      // Verify the transaction was successful
      const txStatus = await this.connection.getTransaction(tx);
      if (txStatus && txStatus.meta?.err === null) {
        // Only update the league status if the transaction was successful
        await this.leagueRepository.update(
          { league_key: leagueKey },
          { status: LeagueStatus.COMPLETED }
        );
      } else {
        throw new Error('Transaction failed on the blockchain');
      }

      return {
        success: true,
        transaction: tx,
      };
    } catch (error) {
      console.error('Error updating league scores:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async distributeLeaguePayouts(
    leagueKey: string,
    firstPlace: string,
    secondPlace: string,
    thirdPlace: string
  ) {
    try {
      const leaguePublicKey = new PublicKey(leagueKey);
      const firstPlacePublicKey = new PublicKey(firstPlace);
      const secondPlacePublicKey = new PublicKey(secondPlace);
      const thirdPlacePublicKey = new PublicKey(thirdPlace);

      // Find the league manager PDA
      const [leagueManagerPda] = PublicKey.findProgramAddressSync(
        [Buffer.from(anchor.utils.bytes.utf8.encode("league_manager"))],
        this.program.programId
      );

      // Find the league participants PDA
      const [leagueParticipantsPda] = PublicKey.findProgramAddressSync(
        [Buffer.from(anchor.utils.bytes.utf8.encode("league_participants")), leaguePublicKey.toBuffer()],
        this.program.programId
      );

      // Find the league escrow PDA
      const [leagueEscrowPda,bump] = PublicKey.findProgramAddressSync(
        [Buffer.from(anchor.utils.bytes.utf8.encode("escrow")), leaguePublicKey.toBuffer()],
        this.program.programId
      );

      // Call the distribute_league_payouts instruction
      const tx = await this.program.methods
        .distributeLeaguePayouts(
          firstPlacePublicKey,
          secondPlacePublicKey,
          thirdPlacePublicKey,
          bump
        )
        .accounts({
          league: leaguePublicKey,
          leagueParticipants: leagueParticipantsPda,
          leagueEscrow: leagueEscrowPda,
          firstPlaceData: firstPlacePublicKey,
          secondPlaceData: secondPlacePublicKey,
          thirdPlaceData: thirdPlacePublicKey,
          leagueManager: leagueManagerPda,
          authority: this.program.provider.publicKey,
          systemProgram: anchor.web3.SystemProgram.programId,
        } as any)
        .rpc({
          skipPreflight: true,
        });

      return {
        success: true,
        transaction: tx,
      };
    } catch (error) {
      console.error('Error distributing league payouts:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async updateByLeagueKey(leagueKey: string, updateData: Partial<League>): Promise<League> {
    const league = await this.leagueRepository.findOne({ where: { league_key: leagueKey } });
    if (!league) {
      throw new Error(`League with key ${leagueKey} not found`);
    }
    
    Object.assign(league, updateData);
    return await this.leagueRepository.save(league);
  }
} 