{"address": "3wkChtVLL1RDuGipRrcHjLZz5B1My9gnj5CBPzwtCxWd", "metadata": {"name": "fantasy_football_league", "version": "0.1.0", "spec": "0.1.0", "description": "Created with <PERSON><PERSON>"}, "instructions": [{"name": "create_league", "discriminator": [129, 229, 70, 201, 64, 57, 180, 164], "accounts": [{"name": "league", "writable": true, "signer": true}, {"name": "league_manager", "writable": true}, {"name": "league_participants", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [108, 101, 97, 103, 117, 101, 95, 112, 97, 114, 116, 105, 99, 105, 112, 97, 110, 116, 115]}, {"kind": "account", "path": "league"}]}}, {"name": "creator", "writable": true, "signer": true}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "entry_fee", "type": "u64"}, {"name": "participant_limit", "type": "u8"}, {"name": "start_time", "type": "i64"}, {"name": "end_time", "type": "i64"}, {"name": "first_place_percentage", "type": "u8"}, {"name": "second_place_percentage", "type": "u8"}, {"name": "third_place_percentage", "type": "u8"}, {"name": "description", "type": "string"}, {"name": "league_type", "type": {"defined": {"name": "LeagueType"}}}, {"name": "premier_leauge_id", "type": "i64"}, {"name": "sesson", "type": "i64"}, {"name": "globle_league_key", "type": {"option": "string"}}, {"name": "week_number", "type": {"option": "i64"}}]}, {"name": "distribute_league_payouts", "discriminator": [176, 27, 216, 123, 93, 196, 125, 64], "accounts": [{"name": "league", "writable": true}, {"name": "league_participants", "writable": true}, {"name": "league_escrow", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [101, 115, 99, 114, 111, 119]}, {"kind": "account", "path": "league"}]}}, {"name": "first_place_data", "writable": true}, {"name": "second_place_data", "writable": true}, {"name": "third_place_data", "writable": true}, {"name": "league_manager", "writable": true}, {"name": "authority", "writable": true, "signer": true}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "first_place", "type": "pubkey"}, {"name": "second_place", "type": "pubkey"}, {"name": "third_place", "type": "pubkey"}, {"name": "bump", "type": "u8"}]}, {"name": "initialize", "docs": ["Initializes the League Manager account", "This function creates a new LeagueManager account that will track:", "- The authority (admin) of the program", "- Total number of leagues created", "", "# Arguments", "* `ctx` - Context containing the accounts needed for initialization", "", "# Returns", "* `Result<()>` - Returns Ok(()) if successful, Err otherwise"], "discriminator": [175, 175, 109, 31, 13, 152, 155, 237], "accounts": [{"name": "league_manager", "docs": ["LeagueManager account to be initialized", "This is a PDA (Program Derived Address) with seeds [\"league_manager\"]"], "writable": true, "pda": {"seeds": [{"kind": "const", "value": [108, 101, 97, 103, 117, 101, 95, 109, 97, 110, 97, 103, 101, 114]}]}}, {"name": "authority", "docs": ["Authority (admin) that signs and pays for the transaction"], "writable": true, "signer": true}, {"name": "system_program", "docs": ["System program required for account creation"], "address": "11111111111111111111111111111111"}], "args": []}, {"name": "register_participant", "discriminator": [248, 112, 38, 215, 226, 230, 249, 40], "accounts": [{"name": "league", "writable": true}, {"name": "league_participants", "writable": true}, {"name": "user_league_data", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [117, 115, 101, 114, 95, 108, 101, 97, 103, 117, 101, 95, 100, 97, 116, 97]}, {"kind": "account", "path": "participant"}, {"kind": "account", "path": "league"}]}}, {"name": "league_escrow", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [101, 115, 99, 114, 111, 119]}, {"kind": "account", "path": "league"}]}}, {"name": "participant", "writable": true, "signer": true}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "team_id", "type": "i64"}]}], "accounts": [{"name": "League", "discriminator": [65, 23, 216, 206, 217, 174, 87, 182]}, {"name": "LeagueManager", "discriminator": [214, 190, 193, 178, 223, 236, 67, 11]}, {"name": "LeagueParticipants", "discriminator": [109, 250, 146, 106, 147, 217, 83, 209]}, {"name": "UserLeagueData", "discriminator": [33, 212, 177, 213, 217, 160, 76, 200]}], "errors": [{"code": 6000, "name": "InvalidEntryFee", "msg": "Entry fee must be greater than 0"}, {"code": 6001, "name": "InvalidParticipantLimit", "msg": "Participant limit must be greater than 0"}, {"code": 6002, "name": "InvalidTimeRange", "msg": "Start time must be before end time"}, {"code": 6003, "name": "InvalidPayoutDistribution", "msg": "Payout distribution must sum to 100"}, {"code": 6004, "name": "InvalidFirstPlacePayout", "msg": "First place payout must be greater than 0"}, {"code": 6005, "name": "InvalidSecondPlacePayout", "msg": "Second place payout must be greater than 0"}, {"code": 6006, "name": "InvalidThirdPlacePayout", "msg": "Third place payout must be greater than 0"}, {"code": 6007, "name": "PayoutOverflow", "msg": "Payout calculation overflow"}, {"code": 6008, "name": "LeagueNotOpen", "msg": "League is not open for registration"}, {"code": 6009, "name": "LeagueNotActive", "msg": "League is not active"}, {"code": 6010, "name": "LeagueFull", "msg": "League is full"}, {"code": 6011, "name": "AlreadyRegistered", "msg": "User is already registered in this league"}, {"code": 6012, "name": "NotRegistered", "msg": "User is not registered in this league"}, {"code": 6013, "name": "UserScoreNotFound", "msg": "User score account not found"}, {"code": 6014, "name": "Unauthorized", "msg": "Unauthorized to perform this action"}, {"code": 6015, "name": "InvalidWinners", "msg": "Winners must be different participants"}, {"code": 6016, "name": "InvalidRanking", "msg": "Winners must be ranked correctly by score"}, {"code": 6017, "name": "InvalidRankAssignment", "msg": "Invalid rank assignment"}, {"code": 6018, "name": "InvalidUserLeagueData", "msg": "Invalid rank InvalidUserLeagueData"}, {"code": 6019, "name": "LeagueStillActive", "msg": "league still active"}], "types": [{"name": "League", "type": {"kind": "struct", "fields": [{"name": "id", "type": "pubkey"}, {"name": "creator", "type": "pubkey"}, {"name": "entry_fee", "type": "u64"}, {"name": "participant_limit", "type": "u8"}, {"name": "start_time", "type": "i64"}, {"name": "end_time", "type": "i64"}, {"name": "status", "type": {"defined": {"name": "LeagueStatus"}}}, {"name": "league_type", "type": {"defined": {"name": "LeagueType"}}}, {"name": "first_place_percentage", "type": "u8"}, {"name": "second_place_percentage", "type": "u8"}, {"name": "third_place_percentage", "type": "u8"}, {"name": "current_participants", "type": "u8"}, {"name": "total_prize_pool", "type": "u64"}, {"name": "description", "type": "string"}, {"name": "premier_leauge_id", "type": "i64"}, {"name": "sesson", "type": "i64"}, {"name": "globle_league_key", "type": {"option": "string"}}, {"name": "week_number", "type": {"option": "i64"}}]}}, {"name": "LeagueManager", "docs": ["LeagueManager account structure", "This account stores global state for the Fantasy Football League program"], "type": {"kind": "struct", "fields": [{"name": "authority", "docs": ["Public key of the program authority (admin)"], "type": "pubkey"}, {"name": "total_leagues", "docs": ["Total number of leagues created in the program"], "type": "u64"}]}}, {"name": "LeagueParticipants", "type": {"kind": "struct", "fields": [{"name": "league_id", "type": "pubkey"}, {"name": "participants", "type": {"vec": "pubkey"}}]}}, {"name": "LeagueStatus", "type": {"kind": "enum", "variants": [{"name": "Registration"}, {"name": "Active"}, {"name": "Completed"}]}}, {"name": "LeagueType", "type": {"kind": "enum", "variants": [{"name": "OneVsOne"}, {"name": "Global"}]}}, {"name": "UserLeagueData", "type": {"kind": "struct", "fields": [{"name": "user", "type": "pubkey"}, {"name": "league_id", "type": "pubkey"}, {"name": "team_selection_hash", "type": {"array": ["u8", 32]}}, {"name": "last_updated", "type": "i64"}, {"name": "team_id", "type": "i64"}]}}]}