import { Injectable } from '@nestjs/common';
import * as anchor from '@coral-xyz/anchor';
import { Connection, Keypair, PublicKey } from '@solana/web3.js';
import { Program } from '@coral-xyz/anchor';
import { SolanaService } from './solana.service';
import * as fs from 'fs';
import * as path from 'path';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { League } from '../../leagues/entities/league.entity';
import { LeagueStatus } from '../../leagues/entities/league.entity';
import { PremierLeague } from 'src/premier-league/entities/premier-league.entity';
import { LeagueType } from '../../leagues/entities/league.entity';

@Injectable()
export class SmartContractService {
  private readonly connection: Connection;
  private readonly program: Program;

  constructor(
    private readonly solanaService: SolanaService,
    @InjectRepository(League)
    private readonly leagueRepository: Repository<League>
  ) {
    // Get the program instance from SolanaService
    this.program = this.solanaService.getProgram();
    this.connection = this.solanaService.getConnection();
  }

  async createLeague(
    entryFee: number,
    participantLimit: number,
    startTime: number,
    endTime: number,
    firstPlacePercentage: number,
    secondPlacePercentage: number,
    thirdPlacePercentage: number,
    description: string,
    premierLeagueId: number,
    sesson: number,
  ) {
    try {
      // Check if a global league already exists for this premierLeagueId
      const existingLeague = await this.leagueRepository.findOne({
        where: {
          premierLeagueId: premierLeagueId,
          type: LeagueType.Global
        }
      });

      if (existingLeague) {
        return {
          success: false,
          error: 'A global league already exists for this Premier League ID'
        };
      }

      // Generate a new public key for the league account
      const leagueAccount = Keypair.generate();

      // Find the league manager PDA
      const [leagueManagerPda] = PublicKey.findProgramAddressSync(
        [Buffer.from(anchor.utils.bytes.utf8.encode("league_manager"))],
        this.program.programId
      );

      // Find the league participants PDA
      // Create a PDA for the league participants
    const [leagueParticipantsPda] = PublicKey.findProgramAddressSync(
      [Buffer.from(anchor.utils.bytes.utf8.encode("league_participants")), leagueAccount.publicKey.toBuffer()],
      this.program.programId
    );

      // Call the create_league instruction
       const tx = await this.program.methods
      .createLeague(
        new anchor.BN(entryFee), // entry_fee: 1 SOL
        participantLimit, // participant_limit: 10 participants
        new anchor.BN(startTime), // start_time
        new anchor.BN(endTime), // end_time
        firstPlacePercentage, // first_place_percentage: 50%
        secondPlacePercentage, // second_place_percentage: 30%
        thirdPlacePercentage , // third_place_percentage: 20%,
        description, // description
        { global: {} } ,
        new anchor.BN(premierLeagueId),
        new anchor.BN(sesson),
        null, // league_key (optional, can be null)
        null // year

      )
      .accounts({
        league: leagueAccount.publicKey,
        leagueManager: leagueManagerPda,
        leagueParticipants: leagueParticipantsPda,
        creator: this.program.provider.publicKey,
        systemProgram: anchor.web3.SystemProgram.programId,
      } as any)
      .signers([leagueAccount])
      .rpc({
            skipPreflight: true,
    });

      return {
        success: true,
        transaction: tx,
        leagueAccount: leagueAccount.publicKey.toString(),
        leagueManager: leagueManagerPda.toString(),
        leagueParticipants: leagueParticipantsPda.toString(),
      };
    } catch (error) {
      console.error('Error creating league:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async create(leagueData: Partial<League>): Promise<League> {
    const league = this.leagueRepository.create(leagueData);
    return await this.leagueRepository.save(league);
  }

  async distributeLeaguePayouts(
    leagueKey: string,
    firstPlace: string,
    secondPlace: string,
    thirdPlace: string
  ) {
    try {
      const leaguePublicKey = new PublicKey(leagueKey);
      const firstPlacePublicKey = new PublicKey(firstPlace);
      const secondPlacePublicKey = new PublicKey(secondPlace);
      const thirdPlacePublicKey = new PublicKey(thirdPlace);

      // Find the league manager PDA
      const [leagueManagerPda] = PublicKey.findProgramAddressSync(
        [Buffer.from(anchor.utils.bytes.utf8.encode("league_manager"))],
        this.program.programId
      );

      // Find the league participants PDA
      const [leagueParticipantsPda] = PublicKey.findProgramAddressSync(
        [Buffer.from(anchor.utils.bytes.utf8.encode("league_participants")), leaguePublicKey.toBuffer()],
        this.program.programId
      );

      // Find the league escrow PDA
      const [leagueEscrowPda,bump] = PublicKey.findProgramAddressSync(
        [Buffer.from(anchor.utils.bytes.utf8.encode("escrow")), leaguePublicKey.toBuffer()],
        this.program.programId
      );

      // Call the distribute_league_payouts instruction
      const tx = await this.program.methods
        .distributeLeaguePayouts(
          firstPlacePublicKey,
          secondPlacePublicKey,
          thirdPlacePublicKey,
          bump
        )
        .accounts({
          league: leaguePublicKey,
          leagueParticipants: leagueParticipantsPda,
          leagueEscrow: leagueEscrowPda,
          firstPlaceData: firstPlacePublicKey,
          secondPlaceData: secondPlacePublicKey,
          thirdPlaceData: thirdPlacePublicKey,
          leagueManager: leagueManagerPda,
          authority: this.program.provider.publicKey,
          systemProgram: anchor.web3.SystemProgram.programId,
        } as any)
        .rpc({
          skipPreflight: true,
        });

      return {
        success: true,
        transaction: tx,
      };
    } catch (error) {
      console.error('Error distributing league payouts:', error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  async updateByLeagueKey(leagueKey: string, updateData: Partial<League>): Promise<League> {
    const league = await this.leagueRepository.findOne({ where: { league_key: leagueKey } });
    if (!league) {
      throw new Error(`League with key ${leagueKey} not found`);
    }
    
    Object.assign(league, updateData);
    return await this.leagueRepository.save(league);
  }
} 