import { Injectable } from '@nestjs/common';
import * as anchor from '@coral-xyz/anchor';
import { Connection, Keypair, PublicKey } from '@solana/web3.js';
import { Program } from '@coral-xyz/anchor';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import * as path from 'path';


@Injectable()
export class SolanaService {
  private readonly connection: Connection;
  private readonly program: Program;
  private readonly wallet: Keypair;
  private readonly provider: anchor.AnchorProvider;

   constructor(private readonly configService: ConfigService) {

    // Initialize connection to Solana network using environment variable
    const rpcUrl = this.configService.get<string>('SOLANA_RPC_URL');
    if (!rpcUrl) {
      throw new Error('SOLANA_RPC_URL environment variable is not set');
    }
    this.connection = new Connection(rpcUrl, 'confirmed');
    
    // Initialize wallet from private key
    const privateKeyString = this.configService.get<string>('SOLANA_PRIVATE_KEY');
    if (!privateKeyString) {
      throw new Error('SOLANA_PRIVATE_KEY environment variable is not set');
    }
    const privateKey = Uint8Array.from(privateKeyString.split(',').map(Number));
    this.wallet = Keypair.fromSecretKey(privateKey);

    // Initialize program with IDL
    this.provider = new anchor.AnchorProvider(
      this.connection,
      new anchor.Wallet(this.wallet),
      { commitment: 'confirmed' }
    );

    const programId = this.configService.get<string>('SOLANA_PROGRAM_ID');
    if (!programId) {
      throw new Error('SOLANA_PROGRAM_ID environment variable is not set');
    }

    const idlPath = path.join(process.cwd(), 'src', 'solana', 'contracts', 'idl.json');
    const idl = JSON.parse(fs.readFileSync(idlPath, "utf8"));    
    this.program = new Program(idl, this.provider);
  }

  getConnection(): Connection {
    return this.connection;
  }

  getProgram(): Program {
    return this.program;
  }

} 