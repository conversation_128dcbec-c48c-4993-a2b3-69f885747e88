import { Controller, Post, Get, Body, UseGuards, Request } from '@nestjs/common';
import { WalletService } from './wallet.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Wallet } from './entities/wallet.entity';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ConnectWalletDto } from './dto/connect-wallet.dto';

@ApiTags('Wallet')
@Controller('wallet')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class WalletController {
  constructor(private readonly walletService: WalletService) {}

  @Post('connect')
  @ApiOperation({ summary: 'Connect a wallet to the user account' })
  @ApiResponse({
    status: 201,
    description: 'Wallet connected successfully',
    type: Wallet
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid wallet address or signature'
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing JWT token'
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - User not found'
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Wallet already connected to another user'
  })
  async connectWallet(
    @Request() req,
    @Body() connectWalletDto: ConnectWalletDto,
  ): Promise<Wallet> {
    return this.walletService.connectWallet(
      req.user.id,
      connectWalletDto.address,
      connectWalletDto.signature
    );
  }

  @Get()
  @ApiOperation({ summary: 'Get user wallet details' })
  @ApiResponse({
    status: 200,
    description: 'Returns the user wallet details',
    type: Wallet
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing JWT token'
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Wallet not found for this user'
  })
  async getUserWallet(@Request() req): Promise<Wallet> {
    return this.walletService.getUserWallet(req.user.id);
  }
} 