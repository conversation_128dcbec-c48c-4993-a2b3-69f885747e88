import { Controller, Post, Get, Delete, Body, UseGuards, Req } from '@nestjs/common';
import { WalletService } from './wallet.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ConnectWalletDto } from './dto/connect-wallet.dto';

@ApiTags('Wallet')
@Controller('wallet')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class WalletController {
  constructor(private readonly walletService: WalletService) {}

  @Post('connect')
  @ApiOperation({ summary: 'Connect a wallet to the user account' })
  @ApiResponse({ status: 200, description: 'Wallet connected successfully or already connected' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 400, description: 'Bad Request - Invalid wallet address or signature' })
  @ApiResponse({ status: 409, description: 'Conflict - Wallet already connected to another user' })
  async connectWallet(
    @Req() req,
    @Body() connectWalletDto: ConnectWalletDto,
  ) {
    // Get user ID from the JWT token's sub field
    const userId = req.user.userId;
    console.log('User ID from token:', userId);
    return this.walletService.connectWallet(userId, connectWalletDto.walletAddress, connectWalletDto.signature);
  }
} 