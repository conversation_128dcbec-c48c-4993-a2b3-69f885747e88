import { ApiProperty } from '@nestjs/swagger';
import { IsString, Length } from 'class-validator';

export class ConnectWalletDto {
  @ApiProperty({
    description: 'The wallet address to connect',
    example: '******************************************',
  })
  @IsString()
  @Length(44, 44, { message: 'Wallet address must be 42 characters long' })
  walletAddress: string;

  @ApiProperty({
    description: 'The signature to verify wallet ownership',
    example: '0x1234567890abcdef...',
  })
  @IsString()
  signature: string;
} 