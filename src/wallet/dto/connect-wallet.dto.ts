import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, Matches } from 'class-validator';

export class ConnectWalletDto {
  @ApiProperty({
    description: 'The Solana wallet address to connect',
    example: '7v91N7iZ9mNicL8WfG6cgSCKyRXydQjLh6UYBWwm6y1M',
    pattern: '^[1-9A-HJ-NP-Za-km-z]{32,44}$'
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[1-9A-HJ-NP-Za-km-z]{32,44}$/, {
    message: 'Invalid wallet address format. Must be a valid Solana address (32-44 characters, base58 encoded).'
  })
  address: string;

  @ApiProperty({
    description: 'The Solana wallet signature for verification',
    example: '2Uw1bpnsXxu3eFyJ4TL2BXD6CqPjVrrMpEJXhs94GXmz...',
    pattern: '^[1-9A-HJ-NP-Za-km-z]{88,}$'
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[1-9A-HJ-NP-Za-km-z]{88,}$/, {
    message: 'Invalid signature format. Must be a valid Solana signature (base58 encoded).'
  })
  signature: string;
} 