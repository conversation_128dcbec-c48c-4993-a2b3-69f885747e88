import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Wallet } from './entities/wallet.entity';
import { User } from '../users/entities/user.entity';

@Injectable()
export class WalletService {
  constructor(
    @InjectRepository(Wallet)
    private walletRepository: Repository<Wallet>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async connectWallet(userId: number, address: string, signature: string): Promise<Wallet> {
    // Check if wallet address already exists
    const existingWallet = await this.walletRepository.findOne({
      where: { address },
    });

    if (existingWallet) {
      // If wallet exists and belongs to the same user, return it
      if (existingWallet.userId === userId) {
        return existingWallet;
      }
      // If wallet exists but belongs to a different user, throw error
      throw new ConflictException('Wallet address already connected to another user');
    }

    // Get user
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Create new wallet
    const wallet = new Wallet();
    wallet.address = address;
    wallet.signature = signature;
    wallet.user = user;
    wallet.userId = userId;

    return this.walletRepository.save(wallet);
  }

  async getUserWallet(userId: number): Promise<Wallet> {
    const wallet = await this.walletRepository.findOne({
      where: { userId },
    });

    if (!wallet) {
      throw new NotFoundException('Wallet not found for this user');
    }

    return wallet;
  }
} 