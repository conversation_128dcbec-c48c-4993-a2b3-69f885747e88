import { Injectable, ConflictException, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { PublicKey } from '@solana/web3.js';

@Injectable()
export class WalletService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async connectWallet(userId: number, walletAddress: string, signature: string): Promise<{ message: string; user: User }> {
    console.log('Connecting wallet for user:', userId, 'with address:', walletAddress);
    
    // Verify the requesting user exists and is authenticated
    const requestingUser = await this.userRepository.findOne({ where: { id: userId } });
    if (!requestingUser) {
      console.log('User not found:', userId);
      throw new UnauthorizedException('User not found or not authenticated');
    }
    console.log('Found requesting user:', requestingUser.id, 'with wallet:', requestingUser.walletAddress);

    // Check if wallet is already connected to another user
    const existingUser = await this.userRepository.findOne({
      where: { walletAddress },
    });
    console.log('Existing user with this wallet:', existingUser ? existingUser.id : 'none');

    if (existingUser && existingUser.id !== userId) {
      console.log('Wallet conflict: connected to different user', existingUser.id);
      throw new ConflictException('Wallet is already connected to another user');
    }

    // If wallet is already connected to the same user, return success message
    if (existingUser && existingUser.id === userId) {
      console.log('Wallet already connected to same user');
      return {
        message: 'Wallet is already connected',
        user: existingUser
      };
    }

    // Verify the wallet address is valid
    try {
      const publicKey = new PublicKey(walletAddress);
      // Convert to string to ensure we store it in the correct format
      walletAddress = publicKey.toString();
    } catch (error) {
      console.log('Invalid wallet address:', error);
      throw new ConflictException('Invalid wallet address');
    }

    // Verify the signature is not empty
    if (!signature || signature.trim() === '') {
      console.log('Invalid signature');
      throw new UnauthorizedException('Invalid signature');
    }

    // Set new wallet data
    requestingUser.walletAddress = walletAddress;
    requestingUser.walletSignature = signature;
    requestingUser.walletConnectedAt = new Date();

    const savedUser = await this.userRepository.save(requestingUser);
    console.log('Successfully connected wallet for user:', savedUser.id);
    return {
      message: 'Wallet connected successfully',
      user: savedUser
    };
  }
} 