import { Controller, Get, UseGuards, Query, Post, Body } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { PremierLeagueService } from './services/premier-league.service';
import { PremierLeagueResponseDto } from './dto/premier-league-response.dto';
import { PremierLeaguePlayersService } from './services/premier-league-players.service';
import { GetPlayersQueryDto } from './dto/get-players-query.dto';
import { PremierLeaguePlayers } from './entities/premier-league-players.entity';
import { PremierLeagueTeamService } from './services/premier-league-team.service';
import { GetTeamsQueryDto } from './dto/get-teams-query.dto';
import { TeamResponseDto } from './dto/team-response.dto';
import { PlayersByPositionResponseDto } from './dto/players-by-position-response.dto';
import { FetchLeagueDataDto } from './dto/fetch-league-data.dto';
import { PremierLeagueFixtureService } from './services/premier-league-fixture.service';
import { GetFixturesQueryDto } from './dto/get-fixtures-query.dto';
import { FixtureResponseDto } from './dto/fixture-response.dto';

@ApiTags('Football Leagues Data')
@Controller('football-leagues')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class PremierLeagueController {
  constructor(
    private readonly premierLeagueService: PremierLeagueService,
    private readonly playersService: PremierLeaguePlayersService,
    private readonly teamService: PremierLeagueTeamService,
    private readonly fixtureService: PremierLeagueFixtureService,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all Football League data with seasons' })
  @ApiResponse({
    status: 200,
    description: 'Returns all Football League data with their seasons',
    type: [PremierLeagueResponseDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async findAll(): Promise<PremierLeagueResponseDto[]> {
    return this.premierLeagueService.findAll();
  }

  @Post('fetch')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Fetch and store league data for a specific league and season' })
  @ApiResponse({
    status: 200,
    description: 'Successfully fetched and stored league data',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  async fetchLeagueData(@Body() fetchDataDto: FetchLeagueDataDto) {
    return this.premierLeagueService.fetchAndStorePremierLeagueData(fetchDataDto);
  }

  @Get('players')
  @ApiOperation({ summary: 'Get all Premier League players grouped by position with optional filters' })
  @ApiResponse({
    status: 200,
    description: 'Returns Premier League players grouped by position matching the filters',
    type: [PlayersByPositionResponseDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getPlayers(@Query() query: GetPlayersQueryDto): Promise<PlayersByPositionResponseDto[]> {
    return this.playersService.getPlayers(query);
  }

  @Get('teams')
  @ApiOperation({ summary: 'Get Premier League teams with optional filters' })
  @ApiResponse({
    status: 200,
    description: 'Returns Premier League teams matching the filters',
    type: [TeamResponseDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getTeams(@Query() query: GetTeamsQueryDto): Promise<TeamResponseDto[]> {
    return this.teamService.getTeams(query);
  }

  @Get('fixtures')
  @UseGuards(RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Get Premier League fixtures with optional filters' })
  @ApiResponse({
    status: 200,
    description: 'Returns Premier League fixtures matching the filters',
    type: [FixtureResponseDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  async getFixtures(@Query() query: GetFixturesQueryDto): Promise<FixtureResponseDto[]> {
    return this.fixtureService.getFixtures(query);
  }
} 