import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PremierLeague } from './entities/premier-league.entity';
import { PremierLeagueResponseDto } from './dto/premier-league-response.dto';

@Injectable()
export class PremierLeagueService {
  constructor(
    @InjectRepository(PremierLeague)
    private readonly premierLeagueRepository: Repository<PremierLeague>,
  ) {}

  async findAll(): Promise<PremierLeagueResponseDto[]> {
    const leagues = await this.premierLeagueRepository.find({
      relations: ['seasons'],
    });
    return leagues;
  }
} 