import { Entity, PrimaryGeneratedColumn, Column, OneToOne, JoinColumn } from 'typeorm';
import { PremierLeagueSeason } from './premier-league-season.entity';

@Entity('premier_league_coverages')
export class PremierLeagueCoverage {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'boolean', default: false })
  fixtures: boolean;

  @Column({ type: 'boolean', default: false })
  standings: boolean;

  @Column({ type: 'boolean', default: false })
  players: boolean;

  @Column({ type: 'boolean', default: false })
  topScorers: boolean;

  @Column({ type: 'boolean', default: false })
  topAssists: boolean;

  @Column({ type: 'boolean', default: false })
  injuries: boolean;

  @Column({ type: 'boolean', default: false })
  predictions: boolean;

  @Column({ type: 'boolean', default: false })
  odds: boolean;

  @OneToOne(() => PremierLeagueSeason, (season: PremierLeagueSeason) => season.coverage)
  @JoinColumn()
  season: PremierLeagueSeason;
} 