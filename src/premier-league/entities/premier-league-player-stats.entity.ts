import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, <PERSON>umn, <PERSON><PERSON><PERSON><PERSON><PERSON>, Jo<PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { PremierLeaguePlayers } from './premier-league-players.entity';
import { PremierLeague } from './premier-league.entity';
import { PremierLeagueSeason } from './premier-league-season.entity';
import { PremierLeagueFixture } from './premier-league-fixture.entity';

@Entity('premier_league_player_stats')
export class PremierLeaguePlayerStats {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'player_id' })
  playerId: number;

  @Column({ name: 'league_id' })
  leagueId: number;

  @Column({ name: 'season_id' })
  seasonId: number;

  @Column({ name: 'fixture_id' })
  fixtureId: number;

  @Column({ nullable: true })
  minutes: number;

  @Column({ nullable: true })
  number: number;

  @Column({ nullable: true })
  position: string;

  @Column({ type: 'decimal', precision: 4, scale: 2, nullable: true })
  rating: number;

  @Column({ nullable: true })
  captain: boolean;

  @Column({ nullable: true })
  substitute: boolean;

  @Column({ nullable: true })
  offsides: number;

  @Column({ nullable: true })
  shotsTotal: number;

  @Column({ nullable: true })
  shotsOn: number;

  @Column({ nullable: true })
  goalsTotal: number;

  @Column({ nullable: true })
  goalsConceded: number;

  @Column({ nullable: true })
  goalsAssists: number;

  @Column({ nullable: true })
  goalsSaves: number;

  @Column({ nullable: true })
  passesTotal: number;

  @Column({ nullable: true })
  passesKey: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  passesAccuracy: number;

  @Column({ nullable: true })
  tacklesTotal: number;

  @Column({ nullable: true })
  tacklesBlocks: number;

  @Column({ nullable: true })
  tacklesInterceptions: number;

  @Column({ nullable: true })
  duelsTotal: number;

  @Column({ nullable: true })
  duelsWon: number;

  @Column({ nullable: true })
  dribblesAttempts: number;

  @Column({ nullable: true })
  dribblesSuccess: number;

  @Column({ nullable: true })
  dribblesPast: number;

  @Column({ nullable: true })
  foulsDrawn: number;

  @Column({ nullable: true })
  foulsCommitted: number;

  @Column({ nullable: true })
  cardsYellow: number;

  @Column({ nullable: true })
  cardsRed: number;

  @Column({ nullable: true })
  penaltyWon: number;

  @Column({ nullable: true })
  penaltyCommitted: number;

  @Column({ nullable: true })
  penaltyScored: number;

  @Column({ nullable: true })
  penaltyMissed: number;

  @Column({ nullable: true })
  penaltySaved: number;

  @ManyToOne(() => PremierLeaguePlayers, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'player_id', referencedColumnName: 'playerId' })
  player: PremierLeaguePlayers;

  @ManyToOne(() => PremierLeague, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'league_id', referencedColumnName: 'leagueId' })
  league: PremierLeague;

  @ManyToOne(() => PremierLeagueSeason, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'season_id', referencedColumnName: 'id' })
  season: PremierLeagueSeason;

  @ManyToOne(() => PremierLeagueFixture, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'fixture_id', referencedColumnName: 'fixtureId' })
  fixture: PremierLeagueFixture;
} 
