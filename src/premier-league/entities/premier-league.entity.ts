import { <PERSON>tity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { PremierLeagueSeason } from './premier-league-season.entity';

@Entity('premier_leagues')
export class PremierLeague {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  type: string;

  @Column({ nullable: true })
  logo: string;

  @Column({ name: 'league_id', unique: true })
  leagueId: number;

  // Country information
  @Column({ nullable: true })
  countryName: string;

  @Column({ nullable: true })
  countryCode: string;

  @Column({ nullable: true })
  countryFlag: string;

  @OneToMany(() => PremierLeagueSeason, (season: PremierLeagueSeason) => season.league)
  seasons: PremierLeagueSeason[];
} 