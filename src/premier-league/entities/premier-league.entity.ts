import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { PremierLeagueSeason } from './premier-league-season.entity';

@Entity('premier_leagues')
export class PremierLeague {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  type: string;

  @Column()
  logo: string;

  @Column({ name: 'league_id', unique: true })
  leagueId: number;

  // Country information
  @Column()
  countryName: string;

  @Column()
  countryCode: string;

  @Column()
  countryFlag: string;

  @OneToMany(() => PremierLeagueSeason, (season: PremierLeagueSeason) => season.league)
  seasons: PremierLeagueSeason[];
} 