import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, <PERSON>umn, <PERSON>To<PERSON>ne, JoinColumn } from 'typeorm';
import { PremierLeague } from './premier-league.entity';
import { PremierLeagueSeason } from './premier-league-season.entity';
import { PremierLeagueTeam } from './premier-league-team.entity';

@Entity('premier_league_fixtures')
export class PremierLeagueFixture {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'fixture_id', unique: true })
  fixtureId: number;

  @Column({ nullable: true })
  referee: string;

  @Column()
  timezone: string;

  @Column()
  date: Date;

  @Column()
  timestamp: number;

  @Column({ type: 'jsonb', nullable: true })
  periods: {
    first: number;
    second: number;
  };

  @Column({ type: 'jsonb' })
  venue: {
    id: number;
    name: string;
    city: string;
  };

  @Column({ type: 'jsonb' })
  status: {
    long: string;
    short: string;
    elapsed: number;
    extra: number | null;
  };

  @Column({ type: 'jsonb' })
  goals: {
    home: number;
    away: number;
  };

  @Column({ type: 'jsonb' })
  score: {
    halftime: {
      home: number;
      away: number;
    };
    fulltime: {
      home: number;
      away: number;
    };
    extratime: {
      home: number | null;
      away: number | null;
    };
    penalty: {
      home: number | null;
      away: number | null;
    };
  };

  @Column()
  round: string;

  @ManyToOne(() => PremierLeague)
  @JoinColumn({ name: 'league_id' })
  league: PremierLeague;

  @ManyToOne(() => PremierLeagueSeason)
  @JoinColumn({ name: 'season_id' })
  season: PremierLeagueSeason;

  @ManyToOne(() => PremierLeagueTeam)
  @JoinColumn({ name: 'home_team_id' })
  homeTeam: PremierLeagueTeam;

  @ManyToOne(() => PremierLeagueTeam)
  @JoinColumn({ name: 'away_team_id' })
  awayTeam: PremierLeagueTeam;
} 