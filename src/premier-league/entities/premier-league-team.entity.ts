import { <PERSON><PERSON><PERSON>, PrimaryC<PERSON>umn, Column, ManyToOne, <PERSON>in<PERSON><PERSON>umn, OneToMany } from 'typeorm';
import { PremierLeague } from './premier-league.entity';
import { PremierLeagueSeason } from './premier-league-season.entity';
import { PremierLeaguePlayers } from './premier-league-players.entity';

@Entity('premier_league_teams')
export class PremierLeagueTeam {
  @PrimaryColumn()
  id: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  code: string;

  @Column({ nullable: true })
  country: string;

  @Column({ nullable: true })
  founded: number;

  @Column()
  national: boolean;

  @Column({ nullable: true })
  logo: string;

  // Venue information
  @Column({ nullable: true })
  venueId: number;

  @Column({ nullable: true })
  venueName: string;

  @Column({ nullable: true })
  venueAddress: string;

  @Column({ nullable: true })
  venueCity: string;

  @Column({ nullable: true })
  venueCapacity: number;

  @Column({ nullable: true })
  venueSurface: string;

  @Column({ nullable: true })
  venueImage: string;

  @OneToMany(() => PremierLeaguePlayers, (player) => player.team, {
    cascade: true,
    eager: false,
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE'
  })
  players: PremierLeaguePlayers[];

  @ManyToOne(() => PremierLeague)
  @JoinColumn({ name: 'league_id' })
  league: PremierLeague;

  @ManyToOne(() => PremierLeagueSeason)
  @JoinColumn({ name: 'season_id' })
  season: PremierLeagueSeason;
} 