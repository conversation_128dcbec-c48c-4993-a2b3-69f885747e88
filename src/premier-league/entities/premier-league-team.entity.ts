import { <PERSON><PERSON><PERSON>, PrimaryC<PERSON>umn, <PERSON>umn, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm';
import { PremierLeague } from './premier-league.entity';
import { PremierLeagueSeason } from './premier-league-season.entity';
import { PremierLeaguePlayers } from './premier-league-players.entity';

@Entity('premier_league_teams')
export class PremierLeagueTeam {
  @PrimaryColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  code: string;

  @Column()
  country: string;

  @Column()
  founded: number;

  @Column()
  national: boolean;

  @Column()
  logo: string;

  // Venue information
  @Column()
  venueId: number;

  @Column()
  venueName: string;

  @Column()
  venueAddress: string;

  @Column()
  venueCity: string;

  @Column()
  venueCapacity: number;

  @Column()
  venueSurface: string;

  @Column()
  venueImage: string;

  @OneToMany(() => PremierLeaguePlayers, (player) => player.team, {
    cascade: true,
    eager: false,
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE'
  })
  players: PremierLeaguePlayers[];

  @ManyToOne(() => PremierLeague)
  @JoinColumn({ name: 'league_id' })
  league: PremierLeague;

  @ManyToOne(() => PremierLeagueSeason)
  @JoinColumn({ name: 'season_id' })
  season: PremierLeagueSeason;
} 