import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, ManyToOne, CreateDateColumn, UpdateDateColumn, JoinColumn } from 'typeorm';
import { PremierLeagueTeam } from './premier-league-team.entity';

@Entity('premier_league_players')
export class PremierLeaguePlayers {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({unique: true})
  playerId: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  age: number;

  @Column({ nullable: true })
  number: number;

  @Column()
  position: string;

  @Column()
  photo: string;

  @Column({ name: 'team_id', nullable: false })
  teamId: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true, default: 3 })
  price: number;

  @Column({ default: true })
  isActive: boolean;

  @ManyToOne(() => PremierLeagueTeam, (team) => team.players, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE'
  })
  @JoinC<PERSON>umn({ name: 'team_id' })
  team: PremierLeagueTeam;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 