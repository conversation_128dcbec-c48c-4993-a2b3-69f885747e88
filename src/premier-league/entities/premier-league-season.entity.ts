import { En<PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, OneToOne, JoinColumn } from 'typeorm';
import { PremierLeague } from './premier-league.entity';
import { PremierLeagueCoverage } from './premier-league-coverage.entity';

@Entity('premier_league_seasons')
export class PremierLeagueSeason {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  year: number;

  @Column()
  startDate: Date;

  @Column()
  endDate: Date;

  @Column()
  currentMatchday: number;

  @Column()
  status: string;

  @ManyToOne(() => PremierLeague, (league: PremierLeague) => league.seasons)
  @JoinColumn({ name: 'league_id' })
  league: PremierLeague;

  @OneToOne(() => PremierLeagueCoverage, coverage => coverage.season)
  @JoinColumn()
  coverage: PremierLeagueCoverage;
} 