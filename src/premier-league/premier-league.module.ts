import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { ScheduleModule } from '@nestjs/schedule';
import { PremierLeagueController } from './premier-league.controller';
import { PremierLeagueService } from './services/premier-league.service';
import { PremierLeague } from './entities/premier-league.entity';
import { PremierLeagueSeason } from './entities/premier-league-season.entity';
import { PremierLeagueCoverage } from './entities/premier-league-coverage.entity';
import { PremierLeagueTeam } from './entities/premier-league-team.entity';
import { PremierLeaguePlayers } from './entities/premier-league-players.entity';
import { PremierLeagueFixture } from './entities/premier-league-fixture.entity';
import { PremierLeaguePlayerStats } from './entities/premier-league-player-stats.entity';
import { PremierLeagueTeamService } from './services/premier-league-team.service';
import { PremierLeaguePlayersService } from './services/premier-league-players.service';
import { PremierLeagueFixtureService } from './services/premier-league-fixture.service';
import { PremierLeaguePlayerStatsService } from './services/premier-league-player-stats.service';
import { FixturesCron } from './crons/fixtures.cron';
import { PlayerStatsCron } from './crons/player-stats.cron';
import { PlayerSquadsCron } from './crons/player-squads.cron';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      PremierLeague,
      PremierLeagueSeason,
      PremierLeagueCoverage,
      PremierLeagueTeam,
      PremierLeaguePlayers,
      PremierLeagueFixture,
      PremierLeaguePlayerStats,
    ]),
    HttpModule,
    ScheduleModule.forRoot(),
  ],
  controllers: [PremierLeagueController],
  providers: [
    PremierLeagueService,
    PremierLeagueTeamService,
    PremierLeaguePlayersService,
    PremierLeagueFixtureService,
    PremierLeaguePlayerStatsService,
    FixturesCron,
    PlayerStatsCron,
    PlayerSquadsCron,
  ],
  exports: [
    PremierLeagueService,
    PremierLeagueTeamService,
    PremierLeaguePlayersService,
    PremierLeagueFixtureService,
    PremierLeaguePlayerStatsService,
  ],
})
export class PremierLeagueModule {} 