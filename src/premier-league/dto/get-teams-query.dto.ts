import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';

export class GetTeamsQueryDto {
  @ApiProperty({ description: 'Team ID', required: false, type: Number })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  team_id?: number;

  @ApiProperty({ description: 'League ID', required: false, type: Number })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  league_id?: number;
} 