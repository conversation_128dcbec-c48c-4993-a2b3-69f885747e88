import { ApiProperty } from '@nestjs/swagger';
import { PremierLeagueSeason } from '../entities/premier-league-season.entity';

export class PremierLeagueResponseDto {
  @ApiProperty({ description: 'Premier League ID' })
  id: number;

  @ApiProperty({ description: 'Premier League name' })
  name: string;

  @ApiProperty({ description: 'Premier League type' })
  type: string;

  @ApiProperty({ description: 'Premier League logo URL' })
  logo: string;

  @ApiProperty({ description: 'Country name' })
  countryName: string;

  @ApiProperty({ description: 'Country code' })
  countryCode: string;

  @ApiProperty({ description: 'Country flag URL' })
  countryFlag: string;

  @ApiProperty({ type: [PremierLeagueSeason], description: 'Premier League seasons' })
  seasons: PremierLeagueSeason[];
} 