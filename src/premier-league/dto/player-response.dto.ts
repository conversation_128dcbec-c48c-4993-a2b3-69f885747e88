import { ApiProperty } from '@nestjs/swagger';
import { PlayerPosition } from '../enums/player-position.enum';

export class TeamInfoDto {
  @ApiProperty({ example: 33 })
  id: number;

  @ApiProperty({ example: 'Manchester United' })
  name: string;

  @ApiProperty({ example: 'MUN' })
  code: string;

  @ApiProperty({ example: 'England' })
  country: string;

  @ApiProperty({ example: 'https://media.api-sports.io/football/teams/33.png' })
  logo: string;
}

export class PlayerResponseDto {
  @ApiProperty({ example: 1 })
  id: number;

  @ApiProperty({ example: 50132 })
  playerId: number;

  @ApiProperty({ example: '<PERSON><PERSON>' })
  name: string;

  @ApiProperty({ enum: PlayerPosition, example: PlayerPosition.GOALKEEPER })
  position: PlayerPosition;

  @ApiProperty({ example: 'https://media.api-sports.io/football/players/50132.png' })
  photo: string;

  @ApiProperty({ example: 33 })
  teamId: number;

  @ApiProperty({ example: 3.00, description: 'Player price in millions', nullable: true })
  price: number;

  @ApiProperty({ type: TeamInfoDto })
  team: TeamInfoDto;
} 