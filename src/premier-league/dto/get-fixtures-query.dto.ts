import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class GetFixturesQueryDto {
  @ApiProperty({
    description: 'League ID',
    example: 39,
    required: true,
  })
  @Type(() => Number)
  @IsNumber()
  leagueId: number;

  @ApiProperty({
    description: 'Season year',
    example: 2023,
    required: true,
  })
  @Type(() => Number)
  @IsNumber()
  season: number;

  @ApiProperty({
    description: 'Team ID to filter fixtures',
    example: 44,
    required: false,
  })
  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  teamId?: number;
} 