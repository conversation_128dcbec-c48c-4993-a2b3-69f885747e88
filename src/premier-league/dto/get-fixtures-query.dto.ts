import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';

export class GetFixturesQueryDto {
  @ApiProperty({
    description: 'League ID',
    example: 39,
    required: true,
  })
  @IsNumber()
  leagueId: number;

  @ApiProperty({
    description: 'Season year',
    example: 2023,
    required: true,
  })
  @IsNumber()
  season: number;

  @ApiProperty({
    description: 'Team ID to filter fixtures',
    example: 44,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  teamId?: number;
} 