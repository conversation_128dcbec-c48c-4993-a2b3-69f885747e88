import { ApiProperty } from '@nestjs/swagger';
import { PremierLeaguePlayers } from '../entities/premier-league-players.entity';

export class PlayersByPositionResponseDto {
  @ApiProperty({ description: 'Position name (e.g., Forward, Midfielder, Defender, Goalkeeper)' })
  position: string;

  @ApiProperty({ type: [PremierLeaguePlayers], description: 'Players in this position' })
  players: PremierLeaguePlayers[];
} 