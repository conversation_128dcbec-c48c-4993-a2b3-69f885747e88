import { ApiProperty } from '@nestjs/swagger';

export class FixtureResponseDto {
  @ApiProperty({ example: 1 })
  id: number;

  @ApiProperty({ example: 1035037 })
  fixtureId: number;

  @ApiProperty({ example: '<PERSON><PERSON> <PERSON>wson', nullable: true })
  referee: string;

  @ApiProperty({ example: 'UTC' })
  timezone: string;

  @ApiProperty({ example: '2023-08-11T19:00:00+00:00' })
  date: Date;

  @ApiProperty({ example: 1691780400 })
  timestamp: number;

  @ApiProperty({
    example: {
      first: 1691780400,
      second: 1691784000
    },
    nullable: true
  })
  periods: {
    first: number;
    second: number;
  };

  @ApiProperty({
    example: {
      id: 512,
      name: 'Turf Moor',
      city: 'Burnley'
    }
  })
  venue: {
    id: number;
    name: string;
    city: string;
  };

  @ApiProperty({
    example: {
      long: 'Match Finished',
      short: 'FT',
      elapsed: 90,
      extra: null
    }
  })
  status: {
    long: string;
    short: string;
    elapsed: number;
    extra: number | null;
  };

  @ApiProperty({
    example: {
      home: 0,
      away: 3
    }
  })
  goals: {
    home: number;
    away: number;
  };

  @ApiProperty({
    example: {
      halftime: {
        home: 0,
        away: 2
      },
      fulltime: {
        home: 0,
        away: 3
      },
      extratime: {
        home: null,
        away: null
      },
      penalty: {
        home: null,
        away: null
      }
    }
  })
  score: {
    halftime: {
      home: number;
      away: number;
    };
    fulltime: {
      home: number;
      away: number;
    };
    extratime: {
      home: number | null;
      away: number | null;
    };
    penalty: {
      home: number | null;
      away: number | null;
    };
  };

  @ApiProperty({ example: 'Regular Season - 1' })
  round: string;

  @ApiProperty({
    example: {
      id: 44,
      name: 'Burnley',
      logo: 'https://media.api-sports.io/football/teams/44.png'
    }
  })
  homeTeam: {
    id: number;
    name: string;
    logo: string;
  };

  @ApiProperty({
    example: {
      id: 50,
      name: 'Manchester City',
      logo: 'https://media.api-sports.io/football/teams/50.png'
    }
  })
  awayTeam: {
    id: number;
    name: string;
    logo: string;
  };
} 