import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';
import { PlayerPosition } from '../enums/player-position.enum';

export class GetPlayersQueryDto {
  @ApiProperty({ description: 'League ID', required: false, type: Number })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  league_id?: number;

  @ApiProperty({ description: 'Season year', required: false, type: Number })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  year?: number;

  @ApiProperty({ description: 'Team ID', required: false, type: Number })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  team_id?: number;

  @ApiProperty({ 
    description: 'Player position', 
    required: false, 
    enum: PlayerPosition,
    example: PlayerPosition.GOALKEEPER 
  })
  @IsOptional()
  @IsEnum(PlayerPosition)
  position?: PlayerPosition;
} 