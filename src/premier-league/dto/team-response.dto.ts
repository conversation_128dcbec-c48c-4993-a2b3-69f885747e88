import { ApiProperty } from '@nestjs/swagger';
import { PremierLeaguePlayers } from '../entities/premier-league-players.entity';
import { PremierLeague } from '../entities/premier-league.entity';
import { PremierLeagueSeason } from '../entities/premier-league-season.entity';

export class TeamResponseDto {
  @ApiProperty({ description: 'Team ID' })
  id: number;

  @ApiProperty({ description: 'Team name' })
  name: string;

  @ApiProperty({ description: 'Team code' })
  code: string;

  @ApiProperty({ description: 'Team country' })
  country: string;

  @ApiProperty({ description: 'Team founded year' })
  founded: number;

  @ApiProperty({ description: 'Whether the team is national' })
  national: boolean;

  @ApiProperty({ description: 'Team logo URL' })
  logo: string;

  @ApiProperty({ description: 'Venue ID' })
  venueId: number;

  @ApiProperty({ description: 'Venue name' })
  venueName: string;

  @ApiProperty({ description: 'Venue address' })
  venueAddress: string;

  @ApiProperty({ description: 'Venue city' })
  venueCity: string;

  @ApiProperty({ description: 'Venue capacity' })
  venueCapacity: number;

  @ApiProperty({ description: 'Venue surface type' })
  venueSurface: string;

  @ApiProperty({ description: 'Venue image URL' })
  venueImage: string;

  @ApiProperty({ type: [PremierLeaguePlayers], description: 'Team players' })
  players: PremierLeaguePlayers[];

  @ApiProperty({ type: PremierLeague, description: 'Associated league' })
  league: PremierLeague;

  @ApiProperty({ type: PremierLeagueSeason, description: 'Associated season' })
  season: PremierLeagueSeason;
} 