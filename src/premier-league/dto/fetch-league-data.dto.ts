import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';

export class FetchLeagueDataDto {
  @ApiProperty({ description: 'League ID (e.g., 39 for Premier League)', required: true })
  @IsNotEmpty()
  @Type(() => Number)
  @IsNumber()
  league_id: number;

  @ApiProperty({ description: 'Season year (e.g., 2023)', required: true })
  @IsNotEmpty()
  @Type(() => Number)
  @IsNumber()
  season: number;
} 