import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { PremierLeagueService } from '../services/premier-league.service';
import { In } from 'typeorm';

@Injectable()
export class PlayerSquadsCron {
  private readonly logger = new Logger(PlayerSquadsCron.name);

  constructor(private readonly premierLeagueService: PremierLeagueService) {}

// @Cron('* * * * *') // Run every minute
  async updatePlayerSquads() {
    this.logger.log('Starting player squads update');
    try {
      const leagues = await this.premierLeagueService.findAll();

      for (const league of leagues) {
        for (const season of league.seasons) {
          try {
            // Get all teams for this league and season
            const teams = await this.premierLeagueService.teamService.findByLeagueAndSeason(league.id, season.id);

            for (const team of teams) {
              try {
                // Add delay between requests to avoid rate limiting
                await this.premierLeagueService.delay(this.premierLeagueService.RATE_LIMIT_DELAY);

                // Get current squad from API
                const playersResponse = await this.premierLeagueService.makeApiRequest(
                  `${this.premierLeagueService.API_URL}/players/squads`,
                  {
                    team: team.id,
                  }
                );

                if (!playersResponse.response || playersResponse.response.length === 0) {
                  this.logger.warn(`No players data received for team ${team.name}`);
                  continue;
                }

                const currentSquad = playersResponse.response[0].players;
                
                // Get existing players for this team from database
                const existingPlayers = await this.premierLeagueService.playersRepository.find({
                  where: { team: { id: team.id } }
                });

                // Create a set of current squad player IDs for quick lookup
                const currentSquadIds = new Set(currentSquad.map(player => player.id));

                // Create a set of existing player IDs for quick lookup
                const existingPlayerIds = new Set(existingPlayers.map(player => player.playerId));

                // Process each player in the current squad
                for (const squadPlayer of currentSquad) {
                  try {
                    // Check if player exists in database (anywhere)
                    const existingPlayer = await this.premierLeagueService.playersRepository.findOne({
                      where: { playerId: squadPlayer.id }
                    });

                    if (existingPlayer) {
                      // Player exists in database
                      if (existingPlayer.teamId !== team.id) {
                        // Player has transferred to a new team
                        this.logger.log(`Player ${existingPlayer.name} has transferred to team ${team.name}`);
                        await this.premierLeagueService.playersRepository.update(
                          { id: existingPlayer.id },
                          { 
                            teamId: team.id,
                            isActive: true,
                            // Update other fields if needed
                          }
                        );
                        this.logger.log(`Successfully updated player ${existingPlayer.name} transfer details`);
                      }
                    } else {
                      // New player, create in database
                      this.logger.log(`Found new player ${squadPlayer.name} for team ${team.name}`);
                      await this.premierLeagueService.playersService.createPlayers([squadPlayer], team.id);
                      this.logger.log(`Successfully added new player ${squadPlayer.name}`);
                    }
                  } catch (error) {
                    this.logger.error(`Failed to process player ${squadPlayer.name}:`, error);
                    continue;
                  }
                }

                // Mark players as inactive if they're not in the current squad
                const playersToDeactivate = existingPlayers.filter(player => !currentSquadIds.has(player.playerId));
                
                if (playersToDeactivate.length > 0) {
                  this.logger.log(`Found ${playersToDeactivate.length} players to deactivate for team ${team.name}`);
                  
                  // Update players to inactive
                  await this.premierLeagueService.playersRepository.update(
                    { id: In(playersToDeactivate.map(p => p.id)) },
                    { isActive: false }
                  );
                  this.logger.log(`Successfully deactivated players for team ${team.name}`);
                }
              } catch (error) {
                this.logger.error(`Failed to update squad for team ${team.name}:`, error);
                continue;
              }
            }
          } catch (error) {
            this.logger.error(`Failed to process league ${league.name} season ${season.year}:`, error);
            continue;
          }
        }
      }

      this.logger.log('Finished updating player squads');
    } catch (error) {
      this.logger.error('Error in updatePlayerSquads cron job:', error);
    }
  }
} 