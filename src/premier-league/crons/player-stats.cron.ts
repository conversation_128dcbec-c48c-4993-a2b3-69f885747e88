import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { PremierLeagueService } from '../services/premier-league.service';

@Injectable()
export class PlayerStatsCron {
  private readonly logger = new Logger(PlayerStatsCron.name);

  constructor(private readonly premierLeagueService: PremierLeagueService) {}

// @Cron('*/3 * * * *') // every 3 minutes
  async updateLivePlayerStats() {
    this.logger.log('Starting live player stats update');
    try {
      const leagues = await this.premierLeagueService.findAll();

      for (const league of leagues) {
        for (const season of league.seasons) {
          try {
            // Add delay between requests to avoid rate limiting
            await this.premierLeagueService.delay(this.premierLeagueService.RATE_LIMIT_DELAY);

            // Get live fixtures directly from API
            const liveFixturesResponse = await this.premierLeagueService.makeApiRequest(
              `${this.premierLeagueService.API_URL}/fixtures`,
              {
                league: league.leagueId,
                season: season.year,
                live: 'all',
              }
            );

            if (!liveFixturesResponse.response || liveFixturesResponse.response.length === 0) {
              this.logger.log(`No live fixtures found for league ${league.name} season ${season.year}`);
              continue;
            }

            this.logger.log(`Found ${liveFixturesResponse.response.length} live fixtures for league ${league.name}`);

            // Process each live fixture
            for (const fixtureData of liveFixturesResponse.response) {
              try {
                // Get players statistics for this fixture
                const playersResponse = await this.premierLeagueService.makeApiRequest(
                  `${this.premierLeagueService.API_URL}/fixtures/players`,
                  {
                    fixture: fixtureData.fixture.id,
                  }
                );

                if (!playersResponse.response || playersResponse.response.length === 0) {
                  this.logger.warn(`No players data received for fixture ${fixtureData.fixture.id}`);
                  continue;
                }

                // Process both home and away team players
                for (const teamData of playersResponse.response) {
                  for (const playerData of teamData.players) {
                    try {
                      const stats = playerData.statistics[0];
                      if (!stats) continue;

                      // Update player stats in database
                      await this.premierLeagueService.playerStatsService.createPlayerStats(
                        {
                          minutes: stats.games.minutes,
                          number: stats.games.number,
                          position: stats.games.position,
                          rating: stats.games.rating,
                          captain: stats.games.captain,
                          substitute: stats.games.substitute,
                          offsides: stats.offsides,
                          shotsTotal: stats.shots.total,
                          shotsOn: stats.shots.on,
                          goalsTotal: stats.goals.total,
                          goalsConceded: stats.goals.conceded,
                          goalsAssists: stats.goals.assists,
                          goalsSaves: stats.goals.saves,
                          passesTotal: stats.passes.total,
                          passesKey: stats.passes.key,
                          passesAccuracy: stats.passes.accuracy,
                          tacklesTotal: stats.tackles.total,
                          tacklesBlocks: stats.tackles.blocks,
                          tacklesInterceptions: stats.tackles.interceptions,
                          duelsTotal: stats.duels.total,
                          duelsWon: stats.duels.won,
                          dribblesAttempts: stats.dribbles.attempts,
                          dribblesSuccess: stats.dribbles.success,
                          dribblesPast: stats.dribbles.past,
                          foulsDrawn: stats.fouls.drawn,
                          foulsCommitted: stats.fouls.committed,
                          cardsYellow: stats.cards.yellow,
                          cardsRed: stats.cards.red,
                          penaltyWon: stats.penalty.won,
                          penaltyCommitted: stats.penalty.committed,
                          penaltyScored: stats.penalty.scored,
                          penaltyMissed: stats.penalty.missed,
                          penaltySaved: stats.penalty.saved,
                        },
                        playerData.player.id,
                        league.leagueId,
                        season.id,
                        fixtureData.fixture.id
                      );

                      this.logger.log(`Successfully updated stats for player ${playerData.player.name}`);
                    } catch (error) {
                      this.logger.error(`Failed to update stats for player ${playerData.player.name}:`, error);
                      continue;
                    }
                  }
                }
              } catch (error) {
                this.logger.error(`Failed to process fixture ${fixtureData.fixture.id}:`, error);
                continue;
              }
            }
          } catch (error) {
            this.logger.error(`Failed to process league ${league.name} season ${season.year}:`, error);
            continue;
          }
        }
      }

      this.logger.log('Finished updating live player stats');
    } catch (error) {
      this.logger.error('Error in updateLivePlayerStats cron job:', error);
    }
  }
} 