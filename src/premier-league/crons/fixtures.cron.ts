import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { PremierLeagueService } from '../services/premier-league.service';

@Injectable()
export class FixturesCron {
  private readonly logger = new Logger(FixturesCron.name);

  constructor(private readonly premierLeagueService: PremierLeagueService) {}

// @Cron('*/1 * * * *') // every 3 minutes
  async updateFixturesDaily() {
    this.logger.log('Starting daily fixtures update');
    try {
      const leagues = await this.premierLeagueService.findAll();

      for (const league of leagues) {
        for (const season of league.seasons) {
          try {
            // Add delay between requests to avoid rate limiting
            await this.premierLeagueService.delay(this.premierLeagueService.RATE_LIMIT_DELAY);

            const fixturesResponse = await this.premierLeagueService.makeApiRequest(
              `${this.premierLeagueService.API_URL}/fixtures`,
              {
                league: league.leagueId,
                season: season.year,
                live: 'all',
              }
            );

            if (!fixturesResponse.response || fixturesResponse.response.length === 0) {
              this.logger.warn(`No fixtures data received for league ${league.name} season ${season.year}`);
              continue;
            }

            // Update existing fixtures
            await this.premierLeagueService.fixtureService.updateFixtures(fixturesResponse.response);
            this.logger.log(`Successfully updated fixtures for league ${league.name} season ${season.year}`);
          } catch (error) {
            this.logger.error(`Failed to update fixtures for league ${league.name} season ${season.year}:`, error);
            continue;
          }
        }
      }
      this.logger.log('Finished daily fixtures update');
    } catch (error) {
      this.logger.error('Error in updateFixturesDaily cron job:', error);
    }
  }
} 