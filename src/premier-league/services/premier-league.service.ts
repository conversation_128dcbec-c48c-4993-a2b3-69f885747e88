import { Injectable, Logger, OnModuleInit, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { HttpService } from '@nestjs/axios';
import { PremierLeague } from '../entities/premier-league.entity';
import { PremierLeagueSeason } from '../entities/premier-league-season.entity';
import { PremierLeagueCoverage } from '../entities/premier-league-coverage.entity';
import { PremierLeagueTeamService } from './premier-league-team.service';
import { PremierLeaguePlayersService } from './premier-league-players.service';
import { PremierLeagueFixtureService } from './premier-league-fixture.service';
import { PremierLeaguePlayerStatsService } from './premier-league-player-stats.service';
import { firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';
import { FetchLeagueDataDto } from '../dto/fetch-league-data.dto';
import { PremierLeaguePlayers } from '../entities/premier-league-players.entity';

@Injectable()
export class PremierLeagueService implements OnModuleInit {
  private readonly logger = new Logger(PremierLeagueService.name);
  public readonly API_URL = 'https://v3.football.api-sports.io';
  public readonly API_KEY = process.env.FOOTBALL_DATA_API_KEY;
  public readonly RATE_LIMIT_DELAY = 2000; // 2 seconds delay between requests
  private readonly MAX_RETRIES = 3;

  constructor(
    @InjectRepository(PremierLeague)
    private premierLeagueRepository: Repository<PremierLeague>,
    @InjectRepository(PremierLeagueSeason)
    private seasonRepository: Repository<PremierLeagueSeason>,
    @InjectRepository(PremierLeagueCoverage)
    private coverageRepository: Repository<PremierLeagueCoverage>,
    @InjectRepository(PremierLeaguePlayers)
    public readonly playersRepository: Repository<PremierLeaguePlayers>,
    private readonly httpService: HttpService,
    public readonly teamService: PremierLeagueTeamService,
    public readonly playersService: PremierLeaguePlayersService,
    public readonly fixtureService: PremierLeagueFixtureService,
    public readonly playerStatsService: PremierLeaguePlayerStatsService,
  ) {
    this.logger.log('PremierLeagueService constructor called');
    if (!this.API_KEY) {
      throw new UnauthorizedException('FOOTBALL_DATA_API_KEY is not set in environment variables');
    }
  }

  onModuleInit() {
    // this.logger.log('PremierLeagueService initialized');
  }

  async findAll(): Promise<PremierLeague[]> {
    return this.premierLeagueRepository.find({
      relations: ['seasons'],
    });
  }

  public async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  public async makeApiRequest(url: string, params: any, retryCount = 0): Promise<any> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: {
            'x-rapidapi-key': this.API_KEY,
            'x-rapidapi-host': 'v3.football.api-sports.io'
          },
          params
        })
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        // Check if it's a rate limit error
        if (error.response?.status === 429 && retryCount < this.MAX_RETRIES) {
          this.logger.warn(`Rate limit hit, retrying in ${this.RATE_LIMIT_DELAY}ms (attempt ${retryCount + 1}/${this.MAX_RETRIES})`);
          await this.delay(this.RATE_LIMIT_DELAY * (retryCount + 1)); // Exponential backoff
          return this.makeApiRequest(url, params, retryCount + 1);
        }
        this.logger.error(`API Error: ${error.message}`, {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
        });
      }
      throw error;
    }
  }

  // Helper function to split array into batches
  private batchArray<T>(array: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < array.length; i += batchSize) {
      batches.push(array.slice(i, i + batchSize));
    }
    return batches;
  }

  async fetchAndStorePremierLeagueData(fetchDataDto: FetchLeagueDataDto) {
    this.logger.log('Starting Premier League data fetch');
    try {
      const leagueId = fetchDataDto.league_id;
      const season = fetchDataDto.season;

      this.logger.log('Making API request to API-Sports.io');
      this.logger.debug(`API Key present: ${!!this.API_KEY}`);

      // Fetch league data
      const leagueResponse = await this.makeApiRequest(`${this.API_URL}/leagues`, {
        id: leagueId,
        season: season,
      });


      if (!leagueResponse.response || leagueResponse.response.length === 0) {
        throw new Error('No league data received from API');
      }

      this.logger.log('League API request successful');
      const leagueData = leagueResponse.response[0];
      this.logger.debug('League Data:', JSON.stringify(leagueData, null, 2));

      // Check if League already exists
      let league = await this.premierLeagueRepository.findOne({
        where: { leagueId: leagueData.league.id },
        relations: ['seasons'],
      });

      if (!league) {
        this.logger.log('Creating new League record');
        league = await this.premierLeagueRepository.save({
          name: leagueData.league.name,
          type: leagueData.league.type,
          logo: leagueData.league.logo,
          leagueId: leagueData.league.id,
          countryName: leagueData.country.name,
          countryCode: leagueData.country.code,
          countryFlag: leagueData.country.flag,
        });
        this.logger.log('Created new League record');
      } else {
        this.logger.log('League record already exists');
        await this.premierLeagueRepository.update(
          { id: league.id },
          {
            name: leagueData.league.name,
            type: leagueData.league.type,
            logo: leagueData.league.logo,
            countryName: leagueData.country.name,
            countryCode: leagueData.country.code,
            countryFlag: leagueData.country.flag,
          }
        );
        this.logger.log('Updated existing League record');
      }

      // Process each season from the response
      for (const seasonData of leagueData.seasons) {
        const seasonYear = new Date(seasonData.start).getFullYear();
        
        let season = await this.seasonRepository.findOne({
          where: { 
            year: seasonYear,
            league: { id: league.id }
          },
          relations: ['coverage'],
        });

        if (!season) {
          this.logger.log(`Creating new season for year ${seasonYear}`);
          season = await this.seasonRepository.save({
            year: seasonYear,
            startDate: new Date(seasonData.start),
            endDate: new Date(seasonData.end),
            currentMatchday: typeof seasonData.current === 'boolean' ? 0 : Number(seasonData.current),
            status: seasonData.current ? 'IN_PLAY' : 'FINISHED',
            league: league,
          });
          this.logger.log(`Created new season for year ${seasonYear}`);

          await this.coverageRepository.save({
            fixtures: true,
            standings: true,
            players: true,
            topScorers: true,
            topAssists: true,
            injuries: true,
            predictions: true,
            odds: true,
            season: season,
          });
          this.logger.log('Created new coverage record');
        } else {
          this.logger.log(`Updating existing season for year ${seasonYear}`);
          await this.seasonRepository.update(
            { id: season.id, league: { id: league.id } },
            {
              currentMatchday: typeof seasonData.current === 'boolean' ? 0 : Number(seasonData.current),
              status: seasonData.current ? 'IN_PLAY' : 'FINISHED',
              startDate: new Date(seasonData.start),
              endDate: new Date(seasonData.end),
            }
          );
          this.logger.log(`Updated existing season for year ${seasonYear}`);
        }

        // Fetch and store teams for this season
        try {
          const teamsResponse = await this.makeApiRequest(`${this.API_URL}/teams`, {
            league: leagueId,
            season: seasonYear,
          });

          if (!teamsResponse.response || teamsResponse.response.length === 0) {
            this.logger.warn(`No teams data received for season ${seasonYear}`);
            continue;
          }

          this.logger.log('Teams API request successful');
          const teamsData = teamsResponse.response;
          
          // Store teams in database
          await this.teamService.createTeams(teamsData, league.id, season.id);
          this.logger.log(`Successfully stored ${teamsData.length} teams for season ${seasonYear}`);

          // Fetch and store fixtures for this season
          try {
            const fixturesResponse = await this.makeApiRequest(`${this.API_URL}/fixtures`, {
              league: leagueId,
              season: seasonYear,
            });

            if (!fixturesResponse.response || fixturesResponse.response.length === 0) {
              this.logger.warn(`No fixtures data received for season ${seasonYear}`);
            } else {
              this.logger.log('Fixtures API request successful');
              await this.fixtureService.createFixtures(fixturesResponse.response, league.id, season.id);
              this.logger.log(`Successfully stored fixtures for season ${seasonYear}`);
            }
          } catch (error) {
            this.logger.error(`Failed to fetch or store fixtures for season ${seasonYear}:`, error);
          }

          // Step 1: Store all players first (in batches)
          const BATCH_SIZE = 7; // 10 requests per minute
          const BATCH_COOLDOWN = 30000; // 60 seconds cooldown between batches
          const teamBatches = this.batchArray(teamsData, BATCH_SIZE);

          for (let batchIndex = 0; batchIndex < teamBatches.length; batchIndex++) {
            const batch = teamBatches[batchIndex];
            this.logger.log(`Processing batch ${batchIndex + 1} of ${teamBatches.length}`);

            for (const team of batch as any[]) {
              try {
                // Add delay between requests to avoid rate limiting
                await this.delay(this.RATE_LIMIT_DELAY);

                const playersResponse = await this.makeApiRequest(`${this.API_URL}/players/squads`, {
                  team: team.team.id,
                });

                console.log(playersResponse);

                if (!playersResponse.response || playersResponse.response.length === 0) {
                  this.logger.warn(`No players data received for team ${team.team.name}`);
                  continue;
                }

                this.logger.log(`Players API request successful for team ${team.team.name}`);
                const playersData = playersResponse.response[0].players;
                
                // Store players in database
                await this.playersService.createPlayers(playersData, team.team.id);
                this.logger.log(`Successfully stored players for team ${team.team.name}`);
              } catch (error) {
                this.logger.error(`Failed to fetch or store players for team ${team.team.name}:`, error);
                continue;
              }
            }

            // Wait for cooldown between batches, except after the last batch
            if (batchIndex < teamBatches.length - 1) {
              this.logger.log(`Waiting ${BATCH_COOLDOWN / 1000} seconds before next batch...`);
              await this.delay(BATCH_COOLDOWN);
            }
          }

          // Step 2: Create default player statistics for all stored players
          const storedPlayers = await this.playersRepository.find({
            where: { team: { league: { id: league.id } } },
            relations: ['team', 'team.league'],
          });

          // Get all fixtures for this league and season
          const fixtures = await this.fixtureService.findAll({
            where: { 
              league: { id: league.id },
              season: { id: season.id }
            }
          });

          this.logger.log(`Creating default statistics for ${storedPlayers.length} players across ${fixtures.length} fixtures`);

          for (const fixture of fixtures) {
            // Get home and away team players for this fixture
            const homeTeamPlayers = await this.playersRepository.find({
              where: { team: { id: fixture.homeTeam.id } },
              relations: ['team'],
            });

            const awayTeamPlayers = await this.playersRepository.find({
              where: { team: { id: fixture.awayTeam.id } },
              relations: ['team'],
            });

            const fixturePlayers = [...homeTeamPlayers, ...awayTeamPlayers];

            for (const player of fixturePlayers) {
              try {
                // Create default statistics with null/0 values matching PremierLeaguePlayerStats structure
                const defaultStats = {
                  minutes: null,
                  number: null,
                  position: null,
                  rating: null,
                  captain: false,
                  substitute: false,
                  offsides: null,
                  shotsTotal: null,
                  shotsOn: null,
                  goalsTotal: null,
                  goalsConceded: 0,
                  goalsAssists: null,
                  goalsSaves: null,
                  passesTotal: null,
                  passesKey: null,
                  passesAccuracy: null,
                  tacklesTotal: null,
                  tacklesBlocks: null,
                  tacklesInterceptions: null,
                  duelsTotal: null,
                  duelsWon: null,
                  dribblesAttempts: null,
                  dribblesSuccess: null,
                  dribblesPast: null,
                  foulsDrawn: null,
                  foulsCommitted: null,
                  cardsYellow: 0,
                  cardsRed: 0,
                  penaltyWon: null,
                  penaltyCommitted: null,
                  penaltyScored: 0,
                  penaltyMissed: 0,
                  penaltySaved: null
                };
                
                // Store default player statistics in database with fixture ID
                await this.playerStatsService.createPlayerStats(
                  defaultStats,
                  player.playerId,
                  league.leagueId,
                  season.id,
                  fixture.fixtureId
                );
                this.logger.log(`Successfully created default statistics for player ${player.name} in fixture ${fixture.fixtureId}`);
              } catch (error) {
                this.logger.error(`Failed to create default statistics for player ${player.name} in fixture ${fixture.fixtureId}:`, error);
                continue;
              }
            }
          }
        } catch (error) {
          this.logger.error(`Failed to fetch or store teams for season ${seasonYear}:`, error);
        }
      }

      return {
        message: 'League data successfully processed',
        league,
      };
    } catch (error) {
      if (error instanceof AxiosError) {
        this.logger.error(`API Error: ${error.message}`, {
          status: error.response?.status,
          statusText: error.response?.statusText,
          data: error.response?.data,
        });
      }
      throw error;
    }
  }
} 
