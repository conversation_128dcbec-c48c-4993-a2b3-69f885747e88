import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PremierLeagueFixture } from '../entities/premier-league-fixture.entity';
import { PremierLeagueTeam } from '../entities/premier-league-team.entity';
import { GetFixturesQueryDto } from '../dto/get-fixtures-query.dto';

@Injectable()
export class PremierLeagueFixtureService {
  private readonly logger = new Logger(PremierLeagueFixtureService.name);

  constructor(
    @InjectRepository(PremierLeagueFixture)
    private fixtureRepository: Repository<PremierLeagueFixture>,
    @InjectRepository(PremierLeagueTeam)
    private teamRepository: Repository<PremierLeagueTeam>,
  ) {}

  async getFixtures(query: GetFixturesQueryDto) {
    const { leagueId, season, teamId } = query;

    const queryBuilder = this.fixtureRepository
      .createQueryBuilder('fixture')
      .leftJoinAndSelect('fixture.league', 'league')
      .leftJoinAndSelect('fixture.season', 'season')
      .leftJoinAndSelect('fixture.homeTeam', 'homeTeam')
      .leftJoinAndSelect('fixture.awayTeam', 'awayTeam')
      .where('league.id = :leagueId', { leagueId })
      .andWhere('season.year = :season', { season });

    if (teamId) {
      queryBuilder.andWhere(
        '(homeTeam.id = :teamId OR awayTeam.id = :teamId)',
        { teamId }
      );
    }

    const fixtures = await queryBuilder.getMany();

    return fixtures.map(fixture => ({
      id: fixture.id,
      fixtureId: fixture.fixtureId,
      referee: fixture.referee,
      timezone: fixture.timezone,
      date: fixture.date,
      timestamp: fixture.timestamp,
      periods: fixture.periods,
      venue: fixture.venue,
      status: fixture.status,
      goals: fixture.goals,
      score: fixture.score,
      round: fixture.round,
      homeTeam: {
        id: fixture.homeTeam.id,
        name: fixture.homeTeam.name,
        logo: fixture.homeTeam.logo,
      },
      awayTeam: {
        id: fixture.awayTeam.id,
        name: fixture.awayTeam.name,
        logo: fixture.awayTeam.logo,
      },
    }));
  }

  async createFixtures(fixturesData: any[], leagueId: number, seasonId: number): Promise<void> {
    this.logger.log('Starting to create fixtures');
    
    for (const fixtureData of fixturesData) {
      try {
        // Find home and away teams
        const homeTeam = await this.teamRepository.findOne({
          where: { id: fixtureData.teams.home.id }
        });
        
        const awayTeam = await this.teamRepository.findOne({
          where: { id: fixtureData.teams.away.id }
        });

        if (!homeTeam || !awayTeam) {
          this.logger.warn(`Skipping fixture ${fixtureData.fixture.id} - Team not found`);
          continue;
        }

        // Check if fixture already exists
        const existingFixture = await this.fixtureRepository.findOne({
          where: { fixtureId: fixtureData.fixture.id }
        });

        if (existingFixture) {
          this.logger.log(`Updating existing fixture ${fixtureData.fixture.id}`);
          await this.fixtureRepository.update(
            { fixtureId: fixtureData.fixture.id },
            {
              referee: fixtureData.fixture.referee,
              timezone: fixtureData.fixture.timezone,
              date: new Date(fixtureData.fixture.date),
              timestamp: fixtureData.fixture.timestamp,
              periods: fixtureData.fixture.periods,
              venue: fixtureData.fixture.venue,
              status: fixtureData.fixture.status,
              goals: fixtureData.goals,
              score: fixtureData.score,
              round: fixtureData.league.round,
              homeTeam,
              awayTeam
            }
          );
        } else {
          this.logger.log(`Creating new fixture ${fixtureData.fixture.id}`);
          await this.fixtureRepository.save({
            fixtureId: fixtureData.fixture.id,
            referee: fixtureData.fixture.referee,
            timezone: fixtureData.fixture.timezone,
            date: new Date(fixtureData.fixture.date),
            timestamp: fixtureData.fixture.timestamp,
            periods: fixtureData.fixture.periods,
            venue: fixtureData.fixture.venue,
            status: fixtureData.fixture.status,
            goals: fixtureData.goals,
            score: fixtureData.score,
            round: fixtureData.league.round,
            league: { id: leagueId },
            season: { id: seasonId },
            homeTeam,
            awayTeam
          });
        }
      } catch (error) {
        this.logger.error(`Error processing fixture ${fixtureData.fixture.id}:`, error);
        continue;
      }
    }
    
    this.logger.log('Finished creating fixtures');
  }

  async getLiveFixtures() {
    const queryBuilder = this.fixtureRepository
      .createQueryBuilder('fixture')
      .leftJoinAndSelect('fixture.league', 'league')
      .leftJoinAndSelect('fixture.season', 'season')
      .leftJoinAndSelect('fixture.homeTeam', 'homeTeam')
      .leftJoinAndSelect('fixture.awayTeam', 'awayTeam')
      .where('fixture.status->>\'short\' IN (:...statuses)', {
        statuses: ['1H', '2H', 'HT', 'ET', 'P', 'BT'] // Live match statuses
      });

    return queryBuilder.getMany();
  }

  async updateFixtures(fixturesData: any[]): Promise<void> {
    this.logger.log('Starting to update fixtures');
    
    for (const fixtureData of fixturesData) {
      try {
        const existingFixture = await this.fixtureRepository.findOne({
          where: { fixtureId: fixtureData.fixture.id }
        });

        if (existingFixture) {
          await this.fixtureRepository.update(
            { fixtureId: fixtureData.fixture.id },
            {
              status: fixtureData.fixture.status,
              goals: fixtureData.goals,
              score: fixtureData.score,
              periods: fixtureData.fixture.periods,
            }
          );
        }
      } catch (error) {
        this.logger.error(`Error updating fixture ${fixtureData.fixture.id}:`, error);
        continue;
      }
    }
    
    this.logger.log('Finished updating fixtures');
  }

  async findAll(query: any): Promise<PremierLeagueFixture[]> {
    return this.fixtureRepository.find({
      where: query.where,
      relations: ['homeTeam', 'awayTeam', 'league', 'season'],
    });
  }

  async getUpcomingRound(premierLeagueId: number, season: number): Promise<{ currentWeek: number; upcomingWeek: number; weekString: string; fixtureDate: Date; lastFixtureDate: Date; lastFixtureId: number }> {
    const currentDate = new Date();
    
    // Get all fixtures for the league and season
    const allFixtures = await this.fixtureRepository
      .createQueryBuilder('fixture')
      .leftJoinAndSelect('fixture.league', 'premierLeague')
      .leftJoinAndSelect('fixture.season', 'season')
      .where('premierLeague.id = :premierLeagueId', { premierLeagueId })
      .andWhere('season.year = :season', { season })
      .orderBy('fixture.date', 'ASC')
      .getMany();

    if (!allFixtures.length) {
      throw new NotFoundException('No fixtures found for this league and season');
    }

    // Helper function to extract week number from round string
    const extractWeekNumber = (roundString: string): number => {
      const match = roundString.match(/Regular Season - (\d+)/);
      return match ? parseInt(match[1]) : 0;
    };

    // Group fixtures by week
    const fixturesByWeek = new Map<number, PremierLeagueFixture[]>();
    allFixtures.forEach(fixture => {
      const weekNumber = extractWeekNumber(fixture.round);
      if (!fixturesByWeek.has(weekNumber)) {
        fixturesByWeek.set(weekNumber, []);
      }
      fixturesByWeek.get(weekNumber)!.push(fixture);
    });

    // Determine current week based on current date
    let currentWeek = 0;
    let upcomingWeek = 0;
    
    for (const [weekNumber, fixtures] of fixturesByWeek) {
      const weekStartDate = new Date(Math.min(...fixtures.map(f => f.date.getTime())));
      const weekEndDate = new Date(Math.max(...fixtures.map(f => f.date.getTime())));
      
      // Check if current date falls within this week
      if (currentDate >= weekStartDate && currentDate <= weekEndDate) {
        currentWeek = weekNumber;
        break;
      }
    }

    // If no current week found, find the next upcoming week
    if (currentWeek === 0) {
      for (const [weekNumber, fixtures] of fixturesByWeek) {
        const weekStartDate = new Date(Math.min(...fixtures.map(f => f.date.getTime())));
        if (weekStartDate > currentDate) {
          upcomingWeek = weekNumber;
          break;
        }
      }
    } else {
      // Find the next week after current week
      const weekNumbers = Array.from(fixturesByWeek.keys()).sort((a, b) => a - b);
      const currentWeekIndex = weekNumbers.indexOf(currentWeek);
      if (currentWeekIndex < weekNumbers.length - 1) {
        upcomingWeek = weekNumbers[currentWeekIndex + 1];
      } else {
        // If current week is the last week, set upcoming week to current week
        upcomingWeek = currentWeek;
      }
    }

    // If no upcoming week found, use the last available week
    if (upcomingWeek === 0) {
      const weekNumbers = Array.from(fixturesByWeek.keys()).sort((a, b) => a - b);
      upcomingWeek = weekNumbers[weekNumbers.length - 1];
    }

    // Get fixtures for the upcoming week
    const upcomingWeekFixtures = fixturesByWeek.get(upcomingWeek) || [];
    
    if (!upcomingWeekFixtures.length) {
      throw new NotFoundException('No fixtures found for the upcoming week');
    }

    // Get the first and last fixture of the upcoming week
    const firstFixture = upcomingWeekFixtures.reduce((earliest, fixture) => 
      fixture.date < earliest.date ? fixture : earliest
    );
    
    const lastFixture = upcomingWeekFixtures.reduce((latest, fixture) => 
      fixture.date > latest.date ? fixture : latest
    );

    return {
      currentWeek,
      upcomingWeek,
      weekString: `Regular Season - ${upcomingWeek}`,
      fixtureDate: firstFixture.date,
      lastFixtureDate: lastFixture.date,
      lastFixtureId: lastFixture.id
    };
  }
} 