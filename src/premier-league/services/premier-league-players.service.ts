import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PremierLeaguePlayers } from '../entities/premier-league-players.entity';
import { PremierLeagueTeam } from '../entities/premier-league-team.entity';
import { GetPlayersQueryDto } from '../dto/get-players-query.dto';
import { PlayerResponseDto } from '../dto/player-response.dto';
import { PlayerPosition } from '../enums/player-position.enum';

@Injectable()
export class PremierLeaguePlayersService {
  private readonly logger = new Logger(PremierLeaguePlayersService.name);

  constructor(
    @InjectRepository(PremierLeaguePlayers)
    private readonly playersRepository: Repository<PremierLeaguePlayers>,
    @InjectRepository(PremierLeagueTeam)
    private readonly teamRepository: Repository<PremierLeagueTeam>,
  ) {}

  async createPlayers(playersData: any[], teamId: number): Promise<void> {
    try {
      const team = await this.teamRepository.findOne({ where: { id: teamId } });
      if (!team) {
        this.logger.error(`Team with ID ${teamId} not found`);
        return;
      }

      for (const playerData of playersData) {
        const existingPlayer = await this.playersRepository.findOne({
          where: { playerId: playerData.id, team: { id: teamId } },
        });

        const playerUpdateData = {
          name: playerData.name,
          age: playerData.age,
          number: playerData.number || null,
          position: playerData.position,
          photo: playerData.photo,
        };

        if (existingPlayer) {
          await this.playersRepository.update(
            { id: existingPlayer.id },
            playerUpdateData,
          );
        } else {
          await this.playersRepository.save({
            playerId: playerData.id,
            ...playerUpdateData,
            team,
          });
        }
      }
      this.logger.log(`Successfully processed ${playersData.length} players for team ${teamId}`);
    } catch (error) {
      this.logger.error(`Error creating players for team ${teamId}:`, error);
      throw error;
    }
  }

  async getPlayers(query: GetPlayersQueryDto): Promise<PlayerResponseDto[]> {
    const queryBuilder = this.playersRepository
      .createQueryBuilder('player')
      .leftJoinAndSelect('player.team', 'team')
      .leftJoinAndSelect('team.league', 'league')
      .leftJoinAndSelect('team.season', 'season');

    if (query.league_id) {
      queryBuilder.andWhere('league.id = :leagueId', { leagueId: query.league_id });
    }

    if (query.year) {
      queryBuilder.andWhere('season.year = :year', { year: query.year });
    }

    if (query.team_id) {
      queryBuilder.andWhere('team.id = :teamId', { teamId: query.team_id });
    }

    if (query.position) {
      queryBuilder.andWhere('player.position = :position', { position: query.position });
    }

    const players = await queryBuilder.getMany();
    
    return players.map(player => ({
      id: player.id,
      playerId: player.playerId,
      name: player.name,
      position: player.position as PlayerPosition,
      photo: player.photo,
      teamId: player.teamId,
      price: player.price,
      team: {
        id: player.team.id,
        name: player.team.name,
        code: player.team.code,
        country: player.team.country,
        logo: player.team.logo
      }
    }));
  }
} 