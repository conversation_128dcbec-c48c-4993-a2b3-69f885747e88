import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PremierLeaguePlayerStats } from '../entities/premier-league-player-stats.entity';

@Injectable()
export class PremierLeaguePlayerStatsService {
  private readonly logger = new Logger(PremierLeaguePlayerStatsService.name);

  constructor(
    @InjectRepository(PremierLeaguePlayerStats)
    private readonly playerStatsRepository: Repository<PremierLeaguePlayerStats>,
  ) {}

  async createPlayerStats(
    statsData: any, 
    playerId: number, 
    leagueId: number, 
    seasonId: number,
    fixtureId: number
  ): Promise<void> {
    try {
      const existingStats = await this.playerStatsRepository.findOne({
        where: {
          playerId,
          leagueId,
          seasonId,
          fixtureId,
        },
      });

      const statsUpdateData: Partial<PremierLeaguePlayerStats> = {
        minutes: statsData.minutes,
        number: statsData.number,
        position: statsData.position,
        rating: statsData.rating,
        captain: statsData.captain,
        substitute: statsData.substitute,
        offsides: statsData.offsides,
        shotsTotal: statsData.shotsTotal,
        shotsOn: statsData.shotsOn,
        goalsTotal: statsData.goalsTotal,
        goalsConceded: statsData.goalsConceded,
        goalsAssists: statsData.goalsAssists,
        goalsSaves: statsData.goalsSaves,
        passesTotal: statsData.passesTotal,
        passesKey: statsData.passesKey,
        passesAccuracy: statsData.passesAccuracy,
        tacklesTotal: statsData.tacklesTotal,
        tacklesBlocks: statsData.tacklesBlocks,
        tacklesInterceptions: statsData.tacklesInterceptions,
        duelsTotal: statsData.duelsTotal,
        duelsWon: statsData.duelsWon,
        dribblesAttempts: statsData.dribblesAttempts,
        dribblesSuccess: statsData.dribblesSuccess,
        dribblesPast: statsData.dribblesPast,
        foulsDrawn: statsData.foulsDrawn,
        foulsCommitted: statsData.foulsCommitted,
        cardsYellow: statsData.cardsYellow,
        cardsRed: statsData.cardsRed,
        penaltyWon: statsData.penaltyWon,
        penaltyCommitted: statsData.penaltyCommitted,
        penaltyScored: statsData.penaltyScored,
        penaltyMissed: statsData.penaltyMissed,
        penaltySaved: statsData.penaltySaved,
      };

      if (existingStats) {
        await this.playerStatsRepository.update(
          { id: existingStats.id },
          statsUpdateData,
        );
        this.logger.log(`Updated stats for player ${playerId} in league ${leagueId} season ${seasonId} fixture ${fixtureId}`);
      } else {
        const newStats = this.playerStatsRepository.create({
          playerId,
          leagueId,
          seasonId,
          fixtureId,
          ...statsUpdateData,
        });
        await this.playerStatsRepository.save(newStats);
        this.logger.log(`Created stats for player ${playerId} in league ${leagueId} season ${seasonId} fixture ${fixtureId}`);
      }
    } catch (error) {
      this.logger.error(`Error creating/updating player stats for player ${playerId}:`, error);
      throw error;
    }
  }

  async updatePlayerStats(
    playerId: number,
    leagueId: number,
    seasonId: number,
    stats: Partial<PremierLeaguePlayerStats>
  ): Promise<void> {
    try {
      const existingStats = await this.playerStatsRepository.findOne({
        where: {
          playerId,
          leagueId,
          seasonId,
        },
      });

      if (existingStats) {
        await this.playerStatsRepository.update(
          { id: existingStats.id },
          stats
        );
      } else {
      }
    } catch (error) {
      this.logger.error(`Failed to update player stats: ${error.message}`);
      throw error;
    }
  }
} 