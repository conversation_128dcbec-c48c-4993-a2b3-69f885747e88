import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PremierLeaguePlayerStats } from '../entities/premier-league-player-stats.entity';

@Injectable()
export class PremierLeaguePlayerStatsService {
  private readonly logger = new Logger(PremierLeaguePlayerStatsService.name);

  constructor(
    @InjectRepository(PremierLeaguePlayerStats)
    private playerStatsRepository: Repository<PremierLeaguePlayerStats>,
  ) {}

  async createPlayerStats(statsData: any, playerId: number, leagueId: number, seasonId: number): Promise<void> {
    try {
      const existingStats = await this.playerStatsRepository.findOne({
        where: {
          playerId,
          leagueId,
          seasonId,
        },
      });

      const statsUpdateData: Partial<PremierLeaguePlayerStats> = {
        appearences: statsData.games?.appearences || undefined,
        lineups: statsData.games?.lineups || undefined,
        minutes: statsData.games?.minutes || undefined,
        number: statsData.games?.number || undefined,
        position: statsData.games?.position || undefined,
        rating: statsData.games?.rating ? parseFloat(statsData.games.rating) : undefined,
        captain: statsData.games?.captain || false,
        substitutesIn: statsData.substitutes?.in || undefined,
        substitutesOut: statsData.substitutes?.out || undefined,
        bench: statsData.substitutes?.bench || undefined,
        shotsTotal: statsData.shots?.total || undefined,
        shotsOn: statsData.shots?.on || undefined,
        goalsTotal: statsData.goals?.total || undefined,
        goalsConceded: statsData.goals?.conceded || undefined,
        goalsAssists: statsData.goals?.assists || undefined,
        goalsSaves: statsData.goals?.saves || undefined,
        passesTotal: statsData.passes?.total || undefined,
        passesKey: statsData.passes?.key || undefined,
        passesAccuracy: statsData.passes?.accuracy ? parseFloat(statsData.passes.accuracy) : undefined,
        tacklesTotal: statsData.tackles?.total || undefined,
        tacklesBlocks: statsData.tackles?.blocks || undefined,
        tacklesInterceptions: statsData.tackles?.interceptions || undefined,
        duelsTotal: statsData.duels?.total || undefined,
        duelsWon: statsData.duels?.won || undefined,
        dribblesAttempts: statsData.dribbles?.attempts || undefined,
        dribblesSuccess: statsData.dribbles?.success || undefined,
        dribblesPast: statsData.dribbles?.past || undefined,
        foulsDrawn: statsData.fouls?.drawn || undefined,
        foulsCommitted: statsData.fouls?.committed || undefined,
        cardsYellow: statsData.cards?.yellow || undefined,
        cardsYellowred: statsData.cards?.yellowred || undefined,
        cardsRed: statsData.cards?.red || undefined,
        penaltyWon: statsData.penalty?.won || undefined,
        penaltyCommitted: statsData.penalty?.commited || undefined,
        penaltyScored: statsData.penalty?.scored || undefined,
        penaltyMissed: statsData.penalty?.missed || undefined,
        penaltySaved: statsData.penalty?.saved || undefined,
      };

      if (existingStats) {
        await this.playerStatsRepository.update(
          { id: existingStats.id },
          statsUpdateData,
        );
        this.logger.log(`Updated stats for player ${playerId} in league ${leagueId} season ${seasonId}`);
      } else {
        const newStats = this.playerStatsRepository.create({
          playerId,
          leagueId,
          seasonId,
          ...statsUpdateData,
        });
        await this.playerStatsRepository.save(newStats);
        this.logger.log(`Created stats for player ${playerId} in league ${leagueId} season ${seasonId}`);
      }
    } catch (error) {
      this.logger.error(`Error creating/updating player stats for player ${playerId}:`, error);
      throw error;
    }
  }

  async updatePlayerStats(
    playerId: number,
    leagueId: number,
    seasonId: number,
    stats: Partial<PremierLeaguePlayerStats>
  ): Promise<void> {
    try {
      const existingStats = await this.playerStatsRepository.findOne({
        where: {
          playerId,
          leagueId,
          seasonId,
        },
      });

      if (existingStats) {
        await this.playerStatsRepository.update(
          { id: existingStats.id },
          stats
        );
      } else {
        // await this.playerStatsRepository.save({
        //   playerId,
        //   leagueId,
        //   seasonId,
        //   ...stats,
        // });
      }
    } catch (error) {
      this.logger.error(`Failed to update player stats: ${error.message}`);
      throw error;
    }
  }
} 