import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PremierLeagueTeam } from '../entities/premier-league-team.entity';
import { GetTeamsQueryDto } from '../dto/get-teams-query.dto';

@Injectable()
export class PremierLeagueTeamService {
  constructor(
    @InjectRepository(PremierLeagueTeam)
    private teamRepository: Repository<PremierLeagueTeam>,
  ) {}

  async createTeams(teamsData: any[], leagueId: number, seasonId: number): Promise<PremierLeagueTeam[]> {
    const teams = teamsData.map(teamData => {
      const team = new PremierLeagueTeam();
      team.id = teamData.team.id;
      team.name = teamData.team.name;
      team.code = teamData.team.code;
      team.country = teamData.team.country;
      team.founded = teamData.team.founded;
      team.national = teamData.team.national;
      team.logo = teamData.team.logo;
      
      // Venue information
      team.venueId = teamData.venue.id;
      team.venueName = teamData.venue.name;
      team.venueAddress = teamData.venue.address;
      team.venueCity = teamData.venue.city;
      team.venueCapacity = teamData.venue.capacity;
      team.venueSurface = teamData.venue.surface;
      team.venueImage = teamData.venue.image;

      // Set relations
      team.league = { id: leagueId } as any;
      team.season = { id: seasonId } as any;

      return team;
    });

    return this.teamRepository.save(teams);
  }

  async findAll(): Promise<PremierLeagueTeam[]> {
    return this.teamRepository.find({
      relations: ['league', 'season'],
    });
  }

  async findByLeagueAndSeason(leagueId: number, seasonId: number): Promise<PremierLeagueTeam[]> {
    return this.teamRepository.find({
      where: {
        league: { id: leagueId },
        season: { id: seasonId },
      },
      relations: ['league', 'season'],
    });
  }

  async getTeams(query: GetTeamsQueryDto): Promise<PremierLeagueTeam[]> {
    const queryBuilder = this.teamRepository
      .createQueryBuilder('team')
      .leftJoinAndSelect('team.league', 'league')
      .leftJoinAndSelect('team.season', 'season')
      .leftJoinAndSelect('team.players', 'players');

    if (query.team_id) {
      queryBuilder.andWhere('team.id = :teamId', { teamId: query.team_id });
    }

    if (query.league_id) {
      queryBuilder.andWhere('league.id = :leagueId', { leagueId: query.league_id });
    }

    return queryBuilder.getMany();
  }
} 