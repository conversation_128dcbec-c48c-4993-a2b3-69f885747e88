import { Controller, Get } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { InjectDataSource } from '@nestjs/typeorm';
import {
  HealthCheck,
  HealthCheckService,
  HealthIndicatorResult,
  MemoryHealthIndicator,
} from '@nestjs/terminus';
import { DataSource } from 'typeorm';

@Controller('health')
@ApiTags('Health Check')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private memory: MemoryHealthIndicator,
    private configService: ConfigService,
    @InjectDataSource() private dataSource: DataSource,
  ) {}

  @Get('database')
  @HealthCheck()
  @ApiOperation({
    summary: 'Check Database Connection',
    description:
      'Performs a health check to verify the database connection is active and responsive.',
  })
  async checkDatabase() {
    return this.health.check([
      async (): Promise<HealthIndicatorResult> => {
        const dbName: string = this.configService.get<string>('DB_DATABASE')!;
        try {
          // Use a simple query instead of pingCheck for SSH tunnel compatibility
          await this.dataSource.query('SELECT 1');

          // Get database information from DataSource options
          // const dbName = this.dataSource.options.database;
          // console.log(dbName);

          const dbHost = (this.dataSource.options as any).host;
          const dbPort = (this.dataSource.options as any).port;

          return {
            [dbName]: {
              status: 'up',
              database: dbName,
              host: dbHost,
              port: dbPort,
            },
          };
        } catch (error) {
          return {
            [dbName]: {
              status: 'down',
              message: error.message,
              database: this.dataSource.options.database,
            },
          };
        }
      },
    ]);
  }

  @Get('memory')
  @HealthCheck()
  @ApiOperation({
    summary: 'Check Memory Usage',
    description:
      'Performs a health check to monitor memory usage, including heap memory and RSS (Resident Set Size).',
  })
  checkMemory() {
    const memSize = 150 * 1024 * 1024; // 150MB

    return this.health.check([
      () => this.memory.checkHeap('memory_heap', memSize),
      () => this.memory.checkRSS('memory_rss', memSize),
    ]);
  }
}
