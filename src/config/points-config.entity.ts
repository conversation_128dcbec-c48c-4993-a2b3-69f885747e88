import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm';

@Entity('points_config')
export class PointsConfig {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', unique: true })
  action: string; // e.g. 'play_<=60', 'play_>60', 'goal_gk_def', 'goal_mid', 'goal_fwd', 'assist'

  @Column({ type: 'int' })
  points: number;

  @Column({ type: 'boolean', default: false })
  isDeduction: boolean; // true for red cards, yellow cards, etc. (Phase 2)
} 