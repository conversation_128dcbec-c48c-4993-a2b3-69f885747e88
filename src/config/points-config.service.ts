import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PointsConfig } from './points-config.entity';
import { PointsConfigDto } from './dto/points-config.dto';

@Injectable()
export class PointsConfigService {
  constructor(
    @InjectRepository(PointsConfig)
    private readonly pointsConfigRepository: Repository<PointsConfig>,
  ) {}

  async getConfig(): Promise<PointsConfig[]> {
    const configs = await this.pointsConfigRepository.find();
    if (!configs.length) {
      throw new NotFoundException('Points config not found');
    }
    return configs;
  }

  async createOrUpdateConfig(dto: PointsConfigDto): Promise<PointsConfig[]> {
    const results: PointsConfig[] = [];
    for (const action of dto.actions) {
      let config = await this.pointsConfigRepository.findOne({ where: { action: action.action } });
      if (!config) {
        config = this.pointsConfigRepository.create(action);
      } else {
        Object.assign(config, action);
      }
      results.push(await this.pointsConfigRepository.save(config));
    }
    return results;
  }
} 