import { S3Client } from '@aws-sdk/client-s3';
import { ConfigService } from '@nestjs/config';
import { Injectable } from '@nestjs/common';

@Injectable()
export class AwsConfigService {
  private readonly s3Client: S3Client;
  private readonly awsAccessKeyId: string;

  constructor(private readonly configService: ConfigService) {
    this.awsAccessKeyId = this.configService.get<string>('AWS_ACCESS_KEY_ID') || '';
    this.s3Client = new S3Client({
      region: this.configService.get<string>('AWS_REGION') || 'us-east-1',
      credentials: {
        accessKeyId: this.awsAccessKeyId,
        secretAccessKey: this.configService.get<string>('AWS_SECRET_ACCESS_KEY') || '',
      },
    });
  }

  getS3Client(): S3Client {
    return this.s3Client;
  }

  getBucketName(): string {
    return this.awsAccessKeyId;
  }
}