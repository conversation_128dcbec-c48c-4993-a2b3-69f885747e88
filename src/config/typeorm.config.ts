import { DataSource } from 'typeorm';
import { User } from '../users/entities/user.entity';
import { League } from '../leagues/entities/league.entity';
import { UserLeagueData } from '../leagues/entities/user-league-data.entity';
import { PremierLeague } from '../premier-league/entities/premier-league.entity';
import { PremierLeagueSeason } from '../premier-league/entities/premier-league-season.entity';
import { PremierLeagueCoverage } from '../premier-league/entities/premier-league-coverage.entity';
import { PremierLeagueTeam } from '../premier-league/entities/premier-league-team.entity';
import { PremierLeaguePlayers } from '../premier-league/entities/premier-league-players.entity';
import { PremierLeagueFixture } from '../premier-league/entities/premier-league-fixture.entity';
import { PremierLeaguePlayerStats } from '../premier-league/entities/premier-league-player-stats.entity';

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: 'localhost',
  port: 5432,
  username: 'rapidinnovation',
  password: '',
  database: 'league_manager',
  entities: [
    User, 
    League, 
    UserLeagueData,
    PremierLeague,
    PremierLeagueSeason,
    PremierLeagueCoverage,
    PremierLeagueTeam,
    PremierLeaguePlayers,
    PremierLeagueFixture,
    PremierLeaguePlayerStats
  ],
  migrations: ['src/migrations/*.ts'],
  synchronize: true, // Temporarily set to true to create tables
}); 