import { MigrationInterface, QueryRunner } from "typeorm";

export class UpdateUserTeamRelations1700000000000 implements MigrationInterface {
    name = 'UpdateUserTeamRelations1700000000000'

    public async up(queryRunner: QueryRunner): Promise<void> {
        // Drop existing foreign key constraints
        await queryRunner.query(`ALTER TABLE "user_teams" DROP CONSTRAINT IF EXISTS "FK_1b0b0b0b0b0b0b0b0b0b0b0b0b0b"`);
        await queryRunner.query(`ALTER TABLE "user_teams" DROP CONSTRAINT IF EXISTS "FK_2b0b0b0b0b0b0b0b0b0b0b0b0b0b"`);
        
        // Drop existing join tables
        await queryRunner.query(`DROP TABLE IF EXISTS "user_teams_goalkeepers_player"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "user_teams_defenders_player"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "user_teams_midfielders_player"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "user_teams_forwards_player"`);

        // Create new join tables with proper constraints
        await queryRunner.query(`
            CREATE TABLE "user_teams_goalkeepers_player" (
                "user_team_id" integer NOT NULL,
                "player_id" integer NOT NULL,
                CONSTRAINT "PK_goalkeepers" PRIMARY KEY ("user_team_id", "player_id")
            )
        `);

        await queryRunner.query(`
            CREATE TABLE "user_teams_defenders_player" (
                "user_team_id" integer NOT NULL,
                "player_id" integer NOT NULL,
                CONSTRAINT "PK_defenders" PRIMARY KEY ("user_team_id", "player_id")
            )
        `);

        await queryRunner.query(`
            CREATE TABLE "user_teams_midfielders_player" (
                "user_team_id" integer NOT NULL,
                "player_id" integer NOT NULL,
                CONSTRAINT "PK_midfielders" PRIMARY KEY ("user_team_id", "player_id")
            )
        `);

        await queryRunner.query(`
            CREATE TABLE "user_teams_forwards_player" (
                "user_team_id" integer NOT NULL,
                "player_id" integer NOT NULL,
                CONSTRAINT "PK_forwards" PRIMARY KEY ("user_team_id", "player_id")
            )
        `);

        // Add foreign key constraints
        await queryRunner.query(`
            ALTER TABLE "user_teams" 
            ADD CONSTRAINT "FK_user_teams_user" 
            FOREIGN KEY ("userId") 
            REFERENCES "user"("id") 
            ON DELETE CASCADE
        `);

        await queryRunner.query(`
            ALTER TABLE "user_teams" 
            ADD CONSTRAINT "FK_user_teams_league" 
            FOREIGN KEY ("leagueId") 
            REFERENCES "league"("id") 
            ON DELETE CASCADE
        `);

        // Add foreign keys for join tables
        await queryRunner.query(`
            ALTER TABLE "user_teams_goalkeepers_player" 
            ADD CONSTRAINT "FK_goalkeepers_user_team" 
            FOREIGN KEY ("user_team_id") 
            REFERENCES "user_teams"("id") 
            ON DELETE CASCADE
        `);

        await queryRunner.query(`
            ALTER TABLE "user_teams_goalkeepers_player" 
            ADD CONSTRAINT "FK_goalkeepers_player" 
            FOREIGN KEY ("player_id") 
            REFERENCES "player"("playerId") 
            ON DELETE CASCADE
        `);

        // Similar foreign key constraints for other positions...
        await queryRunner.query(`
            ALTER TABLE "user_teams_defenders_player" 
            ADD CONSTRAINT "FK_defenders_user_team" 
            FOREIGN KEY ("user_team_id") 
            REFERENCES "user_teams"("id") 
            ON DELETE CASCADE
        `);

        await queryRunner.query(`
            ALTER TABLE "user_teams_defenders_player" 
            ADD CONSTRAINT "FK_defenders_player" 
            FOREIGN KEY ("player_id") 
            REFERENCES "player"("playerId") 
            ON DELETE CASCADE
        `);

        await queryRunner.query(`
            ALTER TABLE "user_teams_midfielders_player" 
            ADD CONSTRAINT "FK_midfielders_user_team" 
            FOREIGN KEY ("user_team_id") 
            REFERENCES "user_teams"("id") 
            ON DELETE CASCADE
        `);

        await queryRunner.query(`
            ALTER TABLE "user_teams_midfielders_player" 
            ADD CONSTRAINT "FK_midfielders_player" 
            FOREIGN KEY ("player_id") 
            REFERENCES "player"("playerId") 
            ON DELETE CASCADE
        `);

        await queryRunner.query(`
            ALTER TABLE "user_teams_forwards_player" 
            ADD CONSTRAINT "FK_forwards_user_team" 
            FOREIGN KEY ("user_team_id") 
            REFERENCES "user_teams"("id") 
            ON DELETE CASCADE
        `);

        await queryRunner.query(`
            ALTER TABLE "user_teams_forwards_player" 
            ADD CONSTRAINT "FK_forwards_player" 
            FOREIGN KEY ("player_id") 
            REFERENCES "player"("playerId") 
            ON DELETE CASCADE
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        // Drop all foreign key constraints
        await queryRunner.query(`ALTER TABLE "user_teams" DROP CONSTRAINT IF EXISTS "FK_user_teams_user"`);
        await queryRunner.query(`ALTER TABLE "user_teams" DROP CONSTRAINT IF EXISTS "FK_user_teams_league"`);
        
        // Drop join tables
        await queryRunner.query(`DROP TABLE IF EXISTS "user_teams_goalkeepers_player"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "user_teams_defenders_player"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "user_teams_midfielders_player"`);
        await queryRunner.query(`DROP TABLE IF EXISTS "user_teams_forwards_player"`);
    }
} 