{"version": "1.0.0", "interfaces": {"League": {"properties": {"id": {"type": "string", "description": "Unique identifier for the league"}, "name": {"type": "string", "description": "Name of the league"}, "description": {"type": "string", "description": "Description of the league"}, "startDate": {"type": "string", "format": "date-time", "description": "Start date of the league"}, "endDate": {"type": "string", "format": "date-time", "description": "End date of the league"}, "status": {"type": "string", "enum": ["PLANNING", "ACTIVE", "COMPLETED", "CANCELLED"], "description": "Current status of the league"}}}, "Team": {"properties": {"id": {"type": "string", "description": "Unique identifier for the team"}, "name": {"type": "string", "description": "Name of the team"}, "leagueId": {"type": "string", "description": "ID of the league this team belongs to"}, "players": {"type": "array", "items": {"type": "string"}, "description": "List of player IDs in the team"}}}, "Player": {"properties": {"id": {"type": "string", "description": "Unique identifier for the player"}, "name": {"type": "string", "description": "Name of the player"}, "email": {"type": "string", "format": "email", "description": "Email address of the player"}, "teamId": {"type": "string", "description": "ID of the team this player belongs to"}}}, "Match": {"properties": {"id": {"type": "string", "description": "Unique identifier for the match"}, "leagueId": {"type": "string", "description": "ID of the league this match belongs to"}, "homeTeamId": {"type": "string", "description": "ID of the home team"}, "awayTeamId": {"type": "string", "description": "ID of the away team"}, "scheduledTime": {"type": "string", "format": "date-time", "description": "Scheduled time for the match"}, "status": {"type": "string", "enum": ["SCHEDULED", "IN_PROGRESS", "COMPLETED", "CANCELLED"], "description": "Current status of the match"}, "score": {"type": "object", "properties": {"home": {"type": "number", "description": "Score of the home team"}, "away": {"type": "number", "description": "Score of the away team"}}}}}}}