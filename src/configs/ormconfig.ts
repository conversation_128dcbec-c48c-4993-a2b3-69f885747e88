import { ConfigService } from '@nestjs/config';
import { join } from 'path';
import { DataSource, DataSourceOptions } from 'typeorm';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';

export function createOrmConfig(): DataSourceOptions & TypeOrmModuleOptions {
  const configService = new ConfigService();

  const ormconfig: DataSourceOptions & TypeOrmModuleOptions = {
    type: 'postgres',
    host: configService.get<string>('DB_HOST'),
    port: +configService.get<string>('DB_PORT')!,
    username: configService.get<string>('DB_USERNAME'),
    password: configService.get<string>('DB_PASSWORD'),
    database: configService.get<string>('DB_DATABASE'),
    entities: [join(__dirname, '..', '**', '**', '*.entity{.ts,.js}')],
    // autoLoadEntities: true,
    synchronize: true,
    // dropSchema: true,
    retryAttempts: 1,
    connectTimeoutMS: 15000,
    migrations: [join(__dirname, '..', 'database', 'migrations', '*{.ts,.js}')],
    // cli: {
    //   migrationsDir: join(__dirname, 'migrations'),
    // },
    migrationsTableName: 'migrations',
    migrationsRun: false,
    maxQueryExecutionTime: 1000,
    logging: true,
    logger: 'file',
    // extra: {
    //   ssl: {
    //     rejectUnauthorized: false,
    //   },
    // },
  };

  return ormconfig;
}

export function createDataSource() {
  return new DataSource(createOrmConfig());
}
