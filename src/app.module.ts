import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from './auth/auth.module';
import { LeaguesModule } from './leagues/leagues.module';
import { WalletModule } from './wallet/wallet.module';
import { PremierLeagueModule } from './premier-league/premier-league.module';
import { PointsConfigModule } from './points-config/points-config.module';
import { createOrmConfig } from './configs/ormconfig';
import { UserProfileModule } from './users/user-profile.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [`.env.${process.env.STAGE}`],
    }),
    TypeOrmModule.forRootAsync({
      useFactory: async () => createOrmConfig(),
    }),
    ScheduleModule.forRoot(),
    AuthModule,
    LeaguesModule,
    WalletModule,
    PremierLeagueModule,
    PointsConfigModule,
    UserProfileModule,
  ],
})
export class AppModule {}
