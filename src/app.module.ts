import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthModule } from './auth/auth.module';
import { LeaguesModule } from './leagues/leagues.module';
import { PremierLeagueModule } from './premier-league/premier-league.module';
import { PointsConfigModule } from './points-config/points-config.module';
import { UserModule } from './users/user.module';
import { HealthModule } from './health/health.module';
import { PostgreSQLDatabaseModule } from './database/postgresql.module';
import { S3Module } from './s3/s3.module';
import { WinstonModule } from 'nest-winston';
import { winstonLoggerConfig } from './configs/winston.config';
import { ThrottlerModule } from '@nestjs/throttler';
import { LoggerMiddleware } from './shared/middlewares/logger.middleware';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [`.env.${process.env.STAGE}`],
    }),
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService) => [
        {
          ttl: +config.get<string>('THROTTLE_TTL')!,
          limit: +config.get<string>('THROTTLE_LIMIT')!,
        },
      ],
    }),
    WinstonModule.forRoot(winstonLoggerConfig),
    PostgreSQLDatabaseModule,
    HealthModule,
    S3Module,
    ScheduleModule.forRoot(),
    AuthModule,
    LeaguesModule,
    PremierLeagueModule,
    PointsConfigModule,
    UserModule,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(LoggerMiddleware).forRoutes('*');
  }
}
