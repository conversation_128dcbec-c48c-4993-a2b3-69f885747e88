import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from './auth/auth.module';
import { User } from './users/entities/user.entity';
import { League } from './leagues/entities/league.entity';
import { LeaguesModule } from './leagues/leagues.module';
import { UserLeagueData } from './leagues/entities/user-league-data.entity';
import { WalletModule } from './wallet/wallet.module';
import { PremierLeague } from './premier-league/entities/premier-league.entity';
import { PremierLeagueSeason } from './premier-league/entities/premier-league-season.entity';
import { PremierLeagueCoverage } from './premier-league/entities/premier-league-coverage.entity';
import { PremierLeagueTeam } from './premier-league/entities/premier-league-team.entity';
import { PremierLeaguePlayers } from './premier-league/entities/premier-league-players.entity';
import { PremierLeagueFixture } from './premier-league/entities/premier-league-fixture.entity';
import { PremierLeagueModule } from './premier-league/premier-league.module';
import { UserTeam } from './leagues/entities/user-team.entity';
import { PremierLeaguePlayerStats } from './premier-league/entities/premier-league-player-stats.entity';
import { PointsConfigModule } from './config/points-config.module';
import { PointsConfig } from './config/points-config.entity';
import { UserTeamScore } from './leagues/entities/user-team-score.entity';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DB_HOST || 'localhost',
      port: 5432,
      username: process.env.DB_USERNAME ,
      password: process.env.DB_PASSWORD ,
      database: process.env.DB_DATABASE ,
      entities: [
        User,
        League,
        UserLeagueData,
        UserTeam,
        PremierLeague,
        PremierLeagueSeason,
        PremierLeagueCoverage,
        PremierLeagueTeam,
        PremierLeaguePlayers,
        PremierLeagueFixture,
        PremierLeaguePlayerStats,
        PointsConfig,
        UserTeamScore,
      ],
      synchronize: true, // Enable in development, disable in production
    }),
    ScheduleModule.forRoot(),
    AuthModule,
    LeaguesModule,
    WalletModule,
    PremierLeagueModule,
    PointsConfigModule,
  ],

})
export class AppModule {
  constructor() {
    console.log('AppModule initialized');
  }
}
