import {
  ClassSerializerInterceptor,
  ValidationPipe,
  VersioningType,
} from '@nestjs/common';
import { NestFactory, Reflector } from '@nestjs/core';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import * as cookieParser from 'cookie-parser';
import * as compression from 'compression';
import helmet from 'helmet';
import * as csurf from 'csurf';
import * as hpp from 'hpp';
import { json, urlencoded } from 'express';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';
import { NestExpressApplication } from '@nestjs/platform-express';
import { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';

/**
 * function for bootstraping the nest application
 */
async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    cors: true,
    bodyParser: true,
    // logger: ['error', 'fatal', 'log', 'verbose', 'warn', 'debug'],
    logger: ['error', 'log'],

  });
  const configService = app.get<ConfigService>(ConfigService);

  app.setGlobalPrefix('/api');
  app.enableVersioning({
    defaultVersion: '1',
    type: VersioningType.URI,
  });

  const corsOptions: CorsOptions = {
    origin: [
      'http://localhost:3000',
      'https://dev.d2n789d55p4neo.amplifyapp.com',
      'https://football-fantasy-api.rapidinnovation.dev',
      'https://dev.d36id1yodcx2hd.amplifyapp.com'
    ],
    methods: ['GET, POST, PATCH, DELETE,PUT'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Cache-Control', 'X-Requested-With'],
    exposedHeaders: ['Content-Type', 'Authorization', 'Cache-Control', 'X-Requested-With'],
    credentials: true,
    optionsSuccessStatus: 204,
    maxAge: 86400,
  };

  app.enableCors(corsOptions);
  app.use(cookieParser());
  app.use(compression());

  app.use(json({ limit: '50kb' }));
  app.use(urlencoded({ extended: true, limit: '50kb' }));

  app.disable('x-powered-by');
  app.set('trust proxy', 1);

  const ignoreMethods =
    configService.get<string>('STAGE') === 'dev'
      ? ['GET', 'HEAD', 'OPTIONS', 'DELETE', 'POST', 'PATCH', 'PUT']
      : ['GET', 'HEAD', 'OPTIONS'];
  app.use(
    csurf({
      cookie: {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'PROD',
        sameSite: 'strict',
      },
      ignoreMethods,
    }),
  );
  app.use(
    helmet({
      hsts: {
        includeSubDomains: true,
        preload: true,
        maxAge: 63072000,
      },
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          defaultSrc: [
            "'self'",
            'https://polyfill.io',
            'https://*.cloudflare.com',
            'http://127.0.0.1:3000/',
          ],
          baseUri: ["'self'"],
          scriptSrc: [
            "'self'",
            'http://127.0.0.1:3000/',
            'https://*.cloudflare.com',
            'https://polyfill.io',
            `https: 'unsafe-inline'`,
          ],
          styleSrc: ["'self'", 'https:', 'http:', "'unsafe-inline'"],
          imgSrc: ["'self'", 'blob:', 'validator.swagger.io'],
          fontSrc: ["'self'", 'https:', 'data:'],
          childSrc: ["'self'", 'blob:'],
          styleSrcAttr: ["'self'", "'unsafe-inline'", 'http:'],
          frameSrc: ["'self'"],
        },
      },
      dnsPrefetchControl: { allow: false },
      frameguard: { action: 'deny' },
      hidePoweredBy: true,
      ieNoOpen: true,
      noSniff: true,
      permittedCrossDomainPolicies: { permittedPolicies: 'none' },
      referrerPolicy: { policy: 'no-referrer' },
      xssFilter: true,
      crossOriginEmbedderPolicy: true,
      crossOriginOpenerPolicy: { policy: 'same-origin-allow-popups' },
      crossOriginResourcePolicy: { policy: 'same-site' },
      originAgentCluster: true,
    }),
  );

  app.use((req: any, res: any, next: any) => {
    res.setHeader(
      'Permissions-Policy',
      'fullscreen=(self), camera=(), geolocation=(self "https://*example.com"), autoplay=(), payment=(), microphone=()',
    );
    next();
  });

  app.use(hpp());

  app.useGlobalPipes(
    new ValidationPipe({ transform: true, stopAtFirstError: true }),
  );
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));

  const stage = configService.get<string>('STAGE')?.toLowerCase() ?? 'dev';
  if (!['prod', 'production'].includes(stage)) {
    const config = new DocumentBuilder()
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'JWT',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      )
      .setTitle('Premier League API')
      .setDescription('The Premier League API description')
      .setVersion('1.0')
      .build();

    const document = SwaggerModule.createDocument(app, config, {
      ignoreGlobalPrefix: false,
    });
    SwaggerModule.setup('api', app, document, {
      swaggerOptions: {
        tagsSorter: 'alpha',
        persistAuthorization: true,
        security: [{ 'JWT-auth': [] }],
      },
    });
  }

  const port = configService.get<string>('PORT') ?? 3000;
  await app.listen(port, () => {
    console.log('Server started on port: ' + port);
  });
}
bootstrap();
