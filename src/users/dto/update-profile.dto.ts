import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl, MaxLength } from 'class-validator';

export class UpdateProfileDto {
  @ApiProperty({
    example: '<PERSON>',
    description: 'The first name of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  @MaxLength(50)
  firstName?: string;

  @ApiProperty({
    example: 'Doe',
    description: 'The last name of the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  @MaxLength(50)
  lastName?: string;

  @ApiProperty({
    example: 'Football enthusiast and fantasy league player',
    description: 'A short biography or description about the user',
    required: false,
  })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  bio?: string;

  @ApiProperty({
    example: 'https://example.com/profile-image.jpg',
    description: 'URL to the user profile image',
    required: false,
  })
  @IsString()
  @IsOptional()
  @IsUrl()
  @MaxLength(255)
  imageUrl?: string;
} 