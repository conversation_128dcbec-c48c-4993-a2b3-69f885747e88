import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsUrl, <PERSON><PERSON>eng<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ength, Matches } from 'class-validator';
import { IsNotAdmin } from '../../shared/decorators/not-admin.decorator';

export class UpdateProfileDto {
  @ApiPropertyOptional({ description: 'First Name of user' })
  @IsNotAdmin()
  @MaxLength(20, { message: 'First Name exceeds given length' })
  @MinLength(1, { message: 'First name has to be of length 1' })
  @Matches(/^[A-Za-z ]+$/)
  @IsString({ message: 'First name must be a string' })
  @IsOptional()
  firstName?: string;

  @ApiPropertyOptional({ description: 'Last Name of user' })
  @IsNotAdmin()
  @MaxLength(20, { message: 'Last Name exceeds given length' })
  @MinLength(1, { message: 'Last name has to be of length 1' })
  @Matches(/^[A-Za-z ]+$/)
  @IsString({ message: 'Last name must be a string' })
  @IsOptional()
  lastName?: string;

  @ApiPropertyOptional({
    example: 'Football enthusiast and fantasy league player',
    description: 'A short biography or description about the user',
  })
  @MaxLength(500)
  @MinLength(1)
  @Matches(/^[A-Za-z ]+$/)
  @IsString({ message: 'Bio must be a string' })
  @IsOptional()
  bio?: string;

  @ApiPropertyOptional({
    example: 'https://s3.amazonaws.com/bucket-name/image.jpg',
    description: "Key of user's profile image in S3 bucket",
  })
  @MaxLength(255)
  @IsString()
  @IsOptional()
  imageKey?: string;
}
