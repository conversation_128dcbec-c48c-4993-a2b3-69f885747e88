import { Controller, Get, Param, UseGuards, Request, Patch, Body, Post, ParseUUIDPipe } from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiUnauthorizedResponse,
  ApiOkResponse,
  ApiNotFoundResponse,
  ApiBadRequestResponse,
  ApiConflictResponse,
} from '@nestjs/swagger';
import { UserService } from './user.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { User } from './entities/user.entity';
import { GetUser } from '../shared/decorators/get-user.decorator';
import { ConnectWalletDto } from './dto/connect-wallet.dto';
import { Wallet } from './entities/wallet.entity';
import { ApiResponseDto, CountApiResponseDto } from '../shared/dto/base-response.dto';
import { ApiSuccessResponse } from '../shared/decorators/swagger-generic-response.decorator';

@ApiTags('User Profile')
@Controller('users')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
@ApiUnauthorizedResponse({ description: 'In case user is not logged in' })
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get('one-vs-one-leagues')
  @ApiOperation({
    summary: 'Get user OneVsOne leagues',
    description:
      'Returns all OneVsOne leagues where the authenticated user is participating, including opponent details and scores.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns user OneVsOne leagues',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          leagueName: { type: 'string' },
          premierLeagueName: { type: 'string' },
          season: { type: 'number' },
          gameWeek: { type: 'number' },
          opponentName: { type: 'string' },
          result: { type: 'string', enum: ['WIN', 'LOSS', 'DRAW', 'PENDING'] },
          scores: {
            type: 'object',
            properties: {
              user: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    gameWeek: { type: 'number' },
                    totalScore: { type: 'number' },
                  },
                },
              },
              opponent: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    gameWeek: { type: 'number' },
                    totalScore: { type: 'number' },
                  },
                },
              },
            },
          },
        },
      },
    },
  })
  async getUserOneVsOneLeagues(@GetUser() user: User) {
    return this.userService.getUserOneVsOneLeagues(user.id);
  }

  @Get(':userId/profile-analytics')
  @ApiOperation({
    summary: 'Get user profile analytics',
    description:
      'Returns the number of completed leagues (wins/losses/draws), win/loss/draw statistics, and currently playing matches by the user across all their wallets in Global and OneVsOne categories.',
  })
  @ApiParam({
    name: 'userId',
    description: 'The ID of the user',
    type: 'number',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description:
      'Returns user profile analytics including completed league counts, win/loss/draw statistics, and currently playing matches across all user wallets.',
    schema: {
      type: 'object',
      properties: {
        globalPlayedCount: {
          type: 'number',
          description: 'Total number of completed Global leagues (wins + losses + draws)',
        },
        oneVsOnePlayedCount: {
          type: 'number',
          description: 'Total number of completed OneVsOne leagues (wins + losses + draws)',
        },
        globalWinCount: {
          type: 'number',
          description: 'Number of Global leagues won',
        },
        globalLossCount: {
          type: 'number',
          description: 'Number of Global leagues lost',
        },
        globalDrawCount: {
          type: 'number',
          description: 'Number of Global leagues drawn',
        },
        oneVsOneWinCount: {
          type: 'number',
          description: 'Number of OneVsOne leagues won',
        },
        oneVsOneLossCount: {
          type: 'number',
          description: 'Number of OneVsOne leagues lost',
        },
        oneVsOneDrawCount: {
          type: 'number',
          description: 'Number of OneVsOne leagues drawn',
        },
        globalCurrentlyPlaying: {
          type: 'number',
          description: 'Number of Global leagues currently being played',
        },
        oneVsOneCurrentlyPlaying: {
          type: 'number',
          description: 'Number of OneVsOne leagues currently being played',
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'No wallets found for this user',
  })
  async getUserProfileAnalytics(@Param('userId', ParseUUIDPipe) userId: string) {
    return this.userService.getUserProfileAnalytics(userId);
  }

  @Get(':userId/global-leaderboard')
  @ApiOperation({
    summary: 'Get user global leaderboard statistics',
    description:
      "Returns the user's global leaderboard statistics including total scores, global rank, and match statistics across all leagues.",
  })
  @ApiParam({
    name: 'userId',
    description: 'The ID of the user',
    type: 'number',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns user global leaderboard statistics',
    schema: {
      type: 'object',
      properties: {
        totalScore: {
          type: 'number',
          description: 'Total score across all teams',
        },
        rank: {
          type: 'number',
          description: 'Global rank based on total scores across all teams',
        },
        leagues: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              leagueId: { type: 'string' },
              leagueName: { type: 'string' },
              totalScore: { type: 'number' },
              matchesPlayed: { type: 'number' },
              wins: { type: 'number' },
              losses: { type: 'number' },
              draws: { type: 'number' },
            },
          },
        },
      },
    },
  })
  @ApiNotFoundResponse({ description: 'User not found' })
  async getUserGlobalLeaderboard(@Param('userId', ParseUUIDPipe) userId: string) {
    return this.userService.getUserGlobalLeaderboard(userId);
  }

  @Post('wallet-connect')
  @ApiOperation({ summary: 'Connect a wallet to the user account' })
  @ApiBadRequestResponse({ description: 'Bad Request - Invalid wallet address or signature' })
  @ApiConflictResponse({ description: 'Conflict - Wallet already connected to another user' })
  @ApiSuccessResponse(Wallet, 'Wallet connected successfully')
  async connectWallet(
    @GetUser() user: User,
    @Body() connectWalletDto: ConnectWalletDto,
  ): Promise<ApiResponseDto<Wallet>> {
    const wallet = await this.userService.connectWallet(user.id, connectWalletDto);

    return { status: 'success', data: wallet };
  }

  @Get('wallets')
  @ApiOperation({ summary: 'Get user wallet details' })
  @ApiSuccessResponse(Wallet, 'Wallets fetched successfully', { isArray: true })
  async getUsersWallets(@GetUser() user: User): Promise<CountApiResponseDto<Wallet[]>> {
    const wallets: Wallet[] = await this.userService.getUserWallets(user.id);

    return { status: 'success', data: wallets, count: wallets.length };
  }

  @Get(':userId')
  @ApiOperation({ summary: 'Get user details', description: 'Returns the basic user details.' })
  @ApiParam({
    name: 'userId',
    description: 'The ID of the user',
    type: 'uuid',
    required: true,
  })
  @ApiSuccessResponse(User, 'User details fetched successfully')
  @ApiNotFoundResponse({ description: 'User not found' })
  async getUserById(@Param('userId', ParseUUIDPipe) userId: string) {
    const user = await this.userService.getUserById(userId);

    return { status: 'success', data: user };
  }

  @Patch()
  @ApiOperation({
    summary: 'Update user profile',
    description: 'Updates the authenticated user profile information.',
  })
  @ApiSuccessResponse(User, 'User Updated successfully')
  @ApiNotFoundResponse({ description: 'User not found' })
  async updateUserProfile(@Body() updateData: UpdateProfileDto, @GetUser() user: User) {
    const updatedUser = await this.userService.updateUserProfile(user.id, updateData);

    return { status: 'success', data: updatedUser };
  }
}
