import {
  Controller,
  Get,
  Param,
  UseGuards,
  Request,
  Patch,
  Body,
  ForbiddenException,
  Post,
  UseInterceptors,
  UploadedFile,
  MaxFileSizeValidator,
  FileTypeValidator,
  ParseFilePipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
} from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { UserProfileService } from '../services/user-profile.service';
import { FileUploadService } from '../services/file-upload.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { UpdateProfileDto } from '../dto/update-profile.dto';
import { Request as ExpressRequest } from 'express';

interface RequestWithUser extends ExpressRequest {
  user: {
    id: number;
    email: string;
    role: string;
  };
}

@ApiTags('User Profile')
@Controller('users')
@ApiBearerAuth('JWT-auth')
export class UserProfileController {
  constructor(
    private readonly userProfileService: UserProfileService,
    private readonly fileUploadService: FileUploadService,
  ) {}

  @Get('one-vs-one-leagues')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get user OneVsOne leagues',
    description: 'Returns all OneVsOne leagues where the authenticated user is participating, including opponent details and scores.',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns user OneVsOne leagues',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          leagueName: { type: 'string' },
          premierLeagueName: { type: 'string' },
          season: { type: 'number' },
          gameWeek: { type: 'number' },
          opponentName: { type: 'string' },
          result: { type: 'string', enum: ['WIN', 'LOSS', 'DRAW', 'PENDING'] },
          scores: {
            type: 'object',
            properties: {
              user: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    gameWeek: { type: 'number' },
                    totalScore: { type: 'number' }
                  }
                }
              },
              opponent: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    gameWeek: { type: 'number' },
                    totalScore: { type: 'number' }
                  }
                }
              }
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  async getUserOneVsOneLeagues(@Request() req: RequestWithUser) {
    return this.userProfileService.getUserOneVsOneLeagues(req.user.id);
  }

  @Get(':userId/profile-analytics')
  @ApiOperation({
    summary: 'Get user profile analytics',
    description: 'Returns the number of completed leagues (wins/losses/draws), win/loss/draw statistics, and currently playing matches by the user across all their wallets in Global and OneVsOne categories.',
  })
  @ApiParam({
    name: 'userId',
    description: 'The ID of the user',
    type: 'number',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns user profile analytics including completed league counts, win/loss/draw statistics, and currently playing matches across all user wallets.',
    schema: {
      type: 'object',
      properties: {
        globalPlayedCount: { 
          type: 'number', 
          description: 'Total number of completed Global leagues (wins + losses + draws)' 
        },
        oneVsOnePlayedCount: { 
          type: 'number', 
          description: 'Total number of completed OneVsOne leagues (wins + losses + draws)' 
        },
        globalWinCount: {
          type: 'number',
          description: 'Number of Global leagues won'
        },
        globalLossCount: {
          type: 'number',
          description: 'Number of Global leagues lost'
        },
        globalDrawCount: {
          type: 'number',
          description: 'Number of Global leagues drawn'
        },
        oneVsOneWinCount: {
          type: 'number',
          description: 'Number of OneVsOne leagues won'
        },
        oneVsOneLossCount: {
          type: 'number',
          description: 'Number of OneVsOne leagues lost'
        },
        oneVsOneDrawCount: {
          type: 'number',
          description: 'Number of OneVsOne leagues drawn'
        },
        globalCurrentlyPlaying: {
          type: 'number',
          description: 'Number of Global leagues currently being played'
        },
        oneVsOneCurrentlyPlaying: {
          type: 'number',
          description: 'Number of OneVsOne leagues currently being played'
        }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'No wallets found for this user',
  })
  async getUserProfileAnalytics(@Param('userId') userId: number) {
    return this.userProfileService.getUserProfileAnalytics(userId);
  }

  @Get(':userId')
  @ApiOperation({
    summary: 'Get user details',
    description: 'Returns the basic user details.',
  })
  @ApiParam({
    name: 'userId',
    description: 'The ID of the user',
    type: 'number',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns user details',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number' },
        email: { type: 'string' },
        firstName: { type: 'string' },
        lastName: { type: 'string' },
        bio: { type: 'string' },
        imageUrl: { type: 'string' },
        role: { type: 'string' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async getUserDetails(@Param('userId') userId: number) {
    return this.userProfileService.getUserDetailsById(userId);
  }

  @Patch('me/profile')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Update user profile',
    description: 'Updates the authenticated user profile information.',
  })
  @ApiResponse({
    status: 200,
    description: 'User profile updated successfully',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'number' },
        email: { type: 'string' },
        firstName: { type: 'string' },
        lastName: { type: 'string' },
        bio: { type: 'string' },
        imageUrl: { type: 'string' },
        role: { type: 'string' },
        createdAt: { type: 'string', format: 'date-time' },
        updatedAt: { type: 'string', format: 'date-time' }
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async updateUserProfile(
    @Body() updateData: UpdateProfileDto,
    @Request() req: RequestWithUser,
  ) {
    return this.userProfileService.updateUserProfile(req.user.id, updateData);
  }

  @Post('upload-image')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(FileInterceptor('file'))
  @ApiBearerAuth('JWT-auth')
  @ApiConsumes('multipart/form-data')
  @ApiOperation({
    summary: 'Upload image',
    description: 'Uploads an image to S3 and returns the image URL. The file must be sent as form-data with the key "file".',
  })
  @ApiResponse({
    status: 200,
    description: 'Image uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        imageUrl: { type: 'string' },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - File is required or invalid file type/size',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'File is required' },
        error: { type: 'string', example: 'Bad Request' },
        statusCode: { type: 'number', example: 400 }
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing JWT token',
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Image file (jpg, jpeg, or png) with maximum size of 5MB'
        }
      }
    }
  })
  async uploadImage(
    @UploadedFile(
      new ParseFilePipe({
        validators: [
          new MaxFileSizeValidator({ maxSize: 5 * 1024 * 1024 }), // 5MB
          new FileTypeValidator({ fileType: /(jpg|jpeg|png)$/ }),
        ],
      }),
    )
    file: Express.Multer.File,
  ) {
    const imageUrl = await this.fileUploadService.uploadFile(file);
    return { imageUrl };
  }

  @Get(':userId/global-leaderboard')
  @ApiOperation({
    summary: 'Get user global leaderboard statistics',
    description: 'Returns the user\'s global leaderboard statistics including total scores, global rank, and match statistics across all leagues.',
  })
  @ApiParam({
    name: 'userId',
    description: 'The ID of the user',
    type: 'number',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns user global leaderboard statistics',
    schema: {
      type: 'object',
      properties: {
        totalScore: { 
          type: 'number',
          description: 'Total score across all teams'
        },
        rank: {
          type: 'number',
          description: 'Global rank based on total scores across all teams'
        },
        leagues: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              leagueId: { type: 'string' },
              leagueName: { type: 'string' },
              totalScore: { type: 'number' },
              matchesPlayed: { type: 'number' },
              wins: { type: 'number' },
              losses: { type: 'number' },
              draws: { type: 'number' }
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
  })
  async getUserGlobalLeaderboard(@Param('userId') userId: number) {
    return this.userProfileService.getUserGlobalLeaderboard(userId);
  }
} 