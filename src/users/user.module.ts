import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserController } from './user.controller';
import { UserService } from './user.service';
import { UserLeagueData } from '../leagues/entities/user-league-data.entity';
import { Wallet } from './entities/wallet.entity';
import { User } from './entities/user.entity';
import { UserTeam } from '../leagues/entities/user-team.entity';
import { UserTeamScore } from '../leagues/entities/user-team-score.entity';
import { League } from '../leagues/entities/league.entity';
import { PremierLeague } from '../premier-league/entities/premier-league.entity';
import { S3Module } from '../s3/s3.module';

@Module({
  imports: [
    S3Module,
    TypeOrmModule.forFeature([UserLeagueData, Wallet, User, UserTeam, UserTeamScore, League, PremierLeague]),
  ],
  controllers: [UserController],
  providers: [UserService],
  exports: [UserService],
})
export class UserModule {}
