import { Module } from '@nestjs/common';
import { UserProfileController } from './controllers/user-profile.controller';
import { UserProfileService } from './services/user-profile.service';
import { FileUploadService } from './services/file-upload.service';

@Module({
  controllers: [UserProfileController],
  providers: [UserProfileService, FileUploadService],
  exports: [UserProfileService],
})
export class UsersModule {} 