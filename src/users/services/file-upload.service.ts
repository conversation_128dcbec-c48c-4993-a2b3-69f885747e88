import { Injectable } from '@nestjs/common';
import { PutObjectCommand } from '@aws-sdk/client-s3';
import { AwsConfigService } from '../../config/aws.config';
import { ConfigService } from '@nestjs/config';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class FileUploadService {
  constructor(
    private readonly awsConfigService: AwsConfigService,
    private readonly configService: ConfigService,
  ) {}

  async uploadFile(file: Express.Multer.File): Promise<string> {
    const fileExtension = file.originalname.split('.').pop();
    const fileName = `${uuidv4()}.${fileExtension}`;
    const bucketName = this.configService.get<string>('AWS_S3_BUCKET_NAME');

    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: `profile-images/${fileName}`,
      Body: file.buffer,
      ContentType: file.mimetype,
    });

    await this.awsConfigService.getS3Client().send(command);

    return `https://${bucketName}.s3.amazonaws.com/profile-images/${fileName}`;
  }
} 