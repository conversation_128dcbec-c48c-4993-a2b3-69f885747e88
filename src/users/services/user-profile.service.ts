import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserLeagueData, MatchStatus } from '../../leagues/entities/user-league-data.entity';
import { Wallet } from '../../wallet/entities/wallet.entity';
import { User } from '../entities/user.entity';
import { UserTeam } from '../../leagues/entities/user-team.entity';
import { UserTeamScore } from '../../leagues/entities/user-team-score.entity';
import { League } from '../../leagues/entities/league.entity';
import { PremierLeague } from '../../premier-league/entities/premier-league.entity';

export interface OneVsOneLeagueResult {
  leagueName: string;
  premierLeagueName: string;
  season: number;
  gameWeek: number;
  opponentName: string;
  result: MatchStatus;
  scores: {
    user: Array<{
      gameWeek: number;
      totalScore: number;
    }>;
    opponent: Array<{
      gameWeek: number;
      totalScore: number;
    }>;
  };
}

@Injectable()
export class UserProfileService {
  constructor(
    @InjectRepository(UserLeagueData)
    private readonly userLeagueDataRepository: Repository<UserLeagueData>,
    @InjectRepository(Wallet)
    private readonly walletRepository: Repository<Wallet>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserTeam)
    private readonly userTeamRepository: Repository<UserTeam>,
    @InjectRepository(UserTeamScore)
    private readonly userTeamScoreRepository: Repository<UserTeamScore>,
    @InjectRepository(League)
    private readonly leagueRepository: Repository<League>,
    @InjectRepository(PremierLeague)
    private readonly premierLeagueRepository: Repository<PremierLeague>,
  ) {}

  async getUserDetailsById(userId: number): Promise<{
    id: number;
    email: string;
    firstName: string;
    lastName: string;
    bio: string;
    imageUrl: string;
    role: string;
    createdAt: Date;
    updatedAt: Date;
  }> {
    // Get user details
    const user = await this.userRepository.findOne({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      bio: user.bio,
      imageUrl: user.imageUrl,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };
  }

  async getUserProfileAnalytics(userId: number): Promise<{
    globalPlayedCount: number;
    oneVsOnePlayedCount: number;
    globalWinCount: number;
    globalLossCount: number;
    globalDrawCount: number;
    oneVsOneWinCount: number;
    oneVsOneLossCount: number;
    oneVsOneDrawCount: number;
    globalCurrentlyPlaying: number;
    oneVsOneCurrentlyPlaying: number;
  }> {
    // First check if user exists
    const user = await this.userRepository.findOne({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Get all user's wallets
    const wallets = await this.walletRepository.find({
      where: { user: { id: userId } }
    });

    // Initialize all counts to zero
    let globalWinCount = 0;
    let globalLossCount = 0;
    let globalDrawCount = 0;
    let oneVsOneWinCount = 0;
    let oneVsOneLossCount = 0;
    let oneVsOneDrawCount = 0;
    let globalCurrentlyPlaying = 0;
    let oneVsOneCurrentlyPlaying = 0;

    // Only query league data if user has wallets
    if (wallets && wallets.length > 0) {
      // Get wallet addresses
      const walletAddresses = wallets.map(wallet => wallet.address);

      // Get all user league participations from all wallets
      const userLeagueData = await this.userLeagueDataRepository
        .createQueryBuilder('userLeagueData')
        .leftJoinAndSelect('userLeagueData.league', 'league')
        .where('userLeagueData.user IN (:...walletAddresses)', { walletAddresses })
        .getMany();

      // Count matches and results for each league type
      for (const data of userLeagueData) {
        if (data.league.type === 'Global') {
          switch (data.status) {
            case MatchStatus.WIN:
              globalWinCount++;
              break;
            case MatchStatus.LOSS:
              globalLossCount++;
              break;
            case MatchStatus.DRAW:
              globalDrawCount++;
              break;
            case MatchStatus.PENDING:
              globalCurrentlyPlaying++;
              break;
          }
        } else if (data.league.type === 'OneVsOne') {
          switch (data.status) {
            case MatchStatus.WIN:
              oneVsOneWinCount++;
              break;
            case MatchStatus.LOSS:
              oneVsOneLossCount++;
              break;
            case MatchStatus.DRAW:
              oneVsOneDrawCount++;
              break;
            case MatchStatus.PENDING:
              oneVsOneCurrentlyPlaying++;
              break;
          }
        }
      }
    }

    // Calculate played counts as sum of wins, losses, and draws
    const globalPlayedCount = globalWinCount + globalLossCount + globalDrawCount;
    const oneVsOnePlayedCount = oneVsOneWinCount + oneVsOneLossCount + oneVsOneDrawCount;

    return {
      globalPlayedCount,
      oneVsOnePlayedCount,
      globalWinCount,
      globalLossCount,
      globalDrawCount,
      oneVsOneWinCount,
      oneVsOneLossCount,
      oneVsOneDrawCount,
      globalCurrentlyPlaying,
      oneVsOneCurrentlyPlaying
    };
  }

  async getUserOneVsOneLeagues(userId: number): Promise<OneVsOneLeagueResult[]> {
    if (!userId || isNaN(userId)) {
      throw new BadRequestException('Invalid user ID');
    }

    // Check if user exists
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // First, get all 1v1 leagues
    const oneVsOneLeagues = await this.leagueRepository
      .createQueryBuilder('league')
      .leftJoinAndSelect('league.premierLeague', 'premierLeague')
      .where('league.type = :leagueType', { leagueType: 'OneVsOne' })
      .getMany();

    const result: OneVsOneLeagueResult[] = [];

    for (const league of oneVsOneLeagues) {
      // Get user's team in this league
      const userTeam = await this.userTeamRepository
        .createQueryBuilder('userTeam')
        .leftJoinAndSelect('userTeam.user', 'user')
        .andWhere('userTeam.user.id = :userId', { userId })
        .getOne();

      if (!userTeam) continue; // Skip if user doesn't have a team in this league

      // Get user's league data
      const userLeagueData = await this.userLeagueDataRepository
        .createQueryBuilder('userLeagueData')
        .where('userLeagueData.league_id = :leagueKey', { leagueKey: league.league_key })
        .andWhere('userLeagueData.user_team_id = :userTeamId', { userTeamId: userTeam.id })
        .getOne();

      if (!userLeagueData) continue; // Skip if no league data found

      // Get opponent's league data
      const opponentLeagueData = await this.userLeagueDataRepository
        .createQueryBuilder('userLeagueData')
        .leftJoinAndSelect('userLeagueData.userTeam', 'userTeam')
        .leftJoinAndSelect('userTeam.user', 'user')
        .where('userLeagueData.league_id = :leagueKey', { leagueKey: league.league_key })
        .andWhere('userLeagueData.user_team_id != :userTeamId', { userTeamId: userTeam.id })
        .getOne();

      // Get scores for both teams for the specific week
      const userTeamScores = await this.userTeamScoreRepository
        .createQueryBuilder('score')
        .select('SUM(score.totalScore)', 'totalScore')
        .where('score.userTeamId = :userTeamId', { userTeamId: userTeam.id })
        .andWhere('score.gameWeek = :weekNumber', { weekNumber: league.week_number })
        .getRawOne();

      const opponentTeamScores = opponentLeagueData ? await this.userTeamScoreRepository
        .createQueryBuilder('score')
        .select('SUM(score.totalScore)', 'totalScore')
        .where('score.userTeamId = :userTeamId', { userTeamId: opponentLeagueData.user_team_id })
        .andWhere('score.gameWeek = :weekNumber', { weekNumber: league.week_number })
        .getRawOne() : null;

      result.push({
        leagueName: league.description,
        premierLeagueName: league.premierLeague?.name,
        season: league.sesson,
        gameWeek: league.week_number,
        opponentName: opponentLeagueData ? `${opponentLeagueData.userTeam.user.firstName} ${opponentLeagueData.userTeam.user.lastName}` : 'No opponent',
        result: userLeagueData.status || MatchStatus.PENDING,
        scores: {
          user: [{
            gameWeek: league.week_number,
            totalScore: parseFloat(userTeamScores?.totalScore || '0')
          }],
          opponent: opponentTeamScores ? [{
            gameWeek: league.week_number,
            totalScore: parseFloat(opponentTeamScores.totalScore || '0')
          }] : []
        }
      });
    }

    return result;
  }

  async updateUserProfile(userId: number, updateData: { 
    firstName?: string; 
    lastName?: string;
    bio?: string;
    imageUrl?: string;
  }): Promise<{
    id: number;
    email: string;
    firstName: string;
    lastName: string;
    bio: string;
    imageUrl: string;
    role: string;
    createdAt: Date;
    updatedAt: Date;
  }> {
    // Get user details
    const user = await this.userRepository.findOne({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Update user fields if provided
    if (updateData.firstName) {
      user.firstName = updateData.firstName;
    }
    if (updateData.lastName) {
      user.lastName = updateData.lastName;
    }
    if (updateData.bio !== undefined) {
      user.bio = updateData.bio;
    }
    if (updateData.imageUrl !== undefined) {
      user.imageUrl = updateData.imageUrl;
    }

    // Save updated user
    await this.userRepository.save(user);

    return {
      id: user.id,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      bio: user.bio,
      imageUrl: user.imageUrl,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };
  }

  async getUserGlobalLeaderboard(userId: number) {
    // Get all user teams with their leagues
    const userTeams = await this.userTeamRepository
      .createQueryBuilder('userTeam')
      .leftJoinAndSelect('userTeam.league', 'league')
      .leftJoinAndSelect('league.premierLeague', 'premierLeague')
      .where('userTeam.user.id = :userId', { userId })
      .getMany();

    if (!userTeams.length) {
      return {
        totalScore: 0,
        rank: 0,
        matchesPlayed: 0,
        wins: 0,
        leagues: []
      };
    }

    // Get all league IDs where user has teams
    const userLeagueIds = userTeams.map(team => team.league.id);

    // Get all teams in the same leagues as user's teams
    const relevantTeams = await this.userTeamRepository
      .createQueryBuilder('userTeam')
      .leftJoinAndSelect('userTeam.user', 'user')
      .where('userTeam.league.id IN (:...leagueIds)', { leagueIds: userLeagueIds })
      .getMany();

    // Get total scores for all relevant teams
    const teamScores = await Promise.all(
      relevantTeams.map(async team => {
        const totalScore = await this.userTeamScoreRepository
          .createQueryBuilder('score')
          .where('score.userTeamId = :userTeamId', { userTeamId: team.id })
          .select('SUM(score.totalScore)', 'total')
          .getRawOne();
        return {
          teamId: team.id,
          userId: team.user.id,
          totalScore: parseFloat(totalScore?.total || '0'),
        };
      })
    );

    // Sort teams by total score
    teamScores.sort((a, b) => b.totalScore - a.totalScore);

    // Calculate user's total score and rank
    const userTotalScore = teamScores
      .filter(score => score.userId === userId)
      .reduce((sum, score) => sum + score.totalScore, 0);

    const rank = teamScores.findIndex(score => score.userId === userId) + 1;

    const leagues = await Promise.all(userTeams.map(async (team) => {
      // Get match statistics
      const userLeagueData = await this.userLeagueDataRepository
        .createQueryBuilder('userLeagueData')
        .where('userLeagueData.user_team_id = :userTeamId', { userTeamId: team.id })
        .andWhere('userLeagueData.league_id = :leagueKey', { leagueKey: team.league.league_key })
        .getMany();

      const matchStats = userLeagueData.reduce((acc, data) => {
        if (data.status === MatchStatus.WIN) acc.wins++;
        else if (data.status === MatchStatus.LOSS) acc.losses++;
        else if (data.status === MatchStatus.DRAW) acc.draws++;
        return acc;
      }, { wins: 0, losses: 0, draws: 0 });

      return {
        leagueId: team.league.league_key,
        leagueName: team.league.premierLeague?.name || team.league.description,
        totalScore: teamScores.find(s => s.teamId === team.id)?.totalScore || 0,
        matchesPlayed: userLeagueData.length,
        ...matchStats
      };
    }));

    // Calculate total matches played and wins from leagues array
    const totalStats = leagues.reduce((acc, league) => {
      acc.matchesPlayed += league.matchesPlayed;
      acc.wins += league.wins;
      return acc;
    }, { matchesPlayed: 0, wins: 0 });

    return {
      totalScore: userTotalScore,
      rank,
      matchesPlayed: totalStats.matchesPlayed,
      wins: totalStats.wins,
    };
  }
} 