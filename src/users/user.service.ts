import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserLeagueData, MatchStatus } from '../leagues/entities/user-league-data.entity';
import { Wallet } from './entities/wallet.entity';
import { User } from './entities/user.entity';
import { UserTeam } from '../leagues/entities/user-team.entity';
import { UserTeamScore } from '../leagues/entities/user-team-score.entity';
import { League } from '../leagues/entities/league.entity';
import { PremierLeague } from '../premier-league/entities/premier-league.entity';
import { UpdateProfileDto } from './dto/update-profile.dto';
import { S3Service } from '../s3/s3.service';
import { ConnectWalletDto } from './dto/connect-wallet.dto';
import { InjectLogger } from '../shared/decorators/logger.decorator';

export interface OneVsOneLeagueResult {
  leagueName: string;
  premierLeagueName: string;
  season: number;
  gameWeek: number;
  opponentName: string;
  result: MatchStatus;
  scores: {
    user: Array<{
      gameWeek: number;
      totalScore: number;
    }>;
    opponent: Array<{
      gameWeek: number;
      totalScore: number;
    }>;
  };
}

@Injectable()
export class UserService {
  constructor(
    @InjectRepository(UserLeagueData)
    private readonly userLeagueDataRepository: Repository<UserLeagueData>,
    @InjectRepository(Wallet)
    private readonly walletRepository: Repository<Wallet>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(UserTeam)
    private readonly userTeamRepository: Repository<UserTeam>,
    @InjectRepository(UserTeamScore)
    private readonly userTeamScoreRepository: Repository<UserTeamScore>,
    @InjectRepository(League)
    private readonly leagueRepository: Repository<League>,
    @InjectRepository(PremierLeague)
    private readonly premierLeagueRepository: Repository<PremierLeague>,
    private readonly s3Service: S3Service,
    @InjectLogger() private readonly logger: Logger,
  ) {}

  async getUserProfileAnalytics(userId: string): Promise<{
    globalPlayedCount: number;
    oneVsOnePlayedCount: number;
    globalWinCount: number;
    globalLossCount: number;
    globalDrawCount: number;
    oneVsOneWinCount: number;
    oneVsOneLossCount: number;
    oneVsOneDrawCount: number;
    globalCurrentlyPlaying: number;
    oneVsOneCurrentlyPlaying: number;
  }> {
    // First check if user exists
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Get all user's wallets
    const wallets = await this.walletRepository.find({
      where: { user: { id: userId } },
    });

    // Initialize all counts to zero
    const counts = this.initializeCounts();

    // Only query league data if user has wallets
    if (wallets && wallets.length > 0) {
      const walletAddresses = wallets.map((wallet) => wallet.address);
      const userLeagueData = await this.getUserLeagueData(walletAddresses);
      this.processLeagueData(userLeagueData, counts);
    }

    // Calculate played counts as sum of wins, losses, and draws
    const globalPlayedCount = counts.globalWinCount + counts.globalLossCount + counts.globalDrawCount;
    const oneVsOnePlayedCount = counts.oneVsOneWinCount + counts.oneVsOneLossCount + counts.oneVsOneDrawCount;

    return {
      globalPlayedCount,
      oneVsOnePlayedCount,
      globalWinCount: counts.globalWinCount,
      globalLossCount: counts.globalLossCount,
      globalDrawCount: counts.globalDrawCount,
      oneVsOneWinCount: counts.oneVsOneWinCount,
      oneVsOneLossCount: counts.oneVsOneLossCount,
      oneVsOneDrawCount: counts.oneVsOneDrawCount,
      globalCurrentlyPlaying: counts.globalCurrentlyPlaying,
      oneVsOneCurrentlyPlaying: counts.oneVsOneCurrentlyPlaying,
    };
  }

  private initializeCounts() {
    return {
      globalWinCount: 0,
      globalLossCount: 0,
      globalDrawCount: 0,
      oneVsOneWinCount: 0,
      oneVsOneLossCount: 0,
      oneVsOneDrawCount: 0,
      globalCurrentlyPlaying: 0,
      oneVsOneCurrentlyPlaying: 0,
    };
  }

  private async getUserLeagueData(walletAddresses: string[]) {
    return this.userLeagueDataRepository
      .createQueryBuilder('userLeagueData')
      .leftJoinAndSelect('userLeagueData.league', 'league')
      .where('userLeagueData.user IN (:...walletAddresses)', {
        walletAddresses,
      })
      .getMany();
  }

  private processLeagueData(userLeagueData: any[], counts: any) {
    for (const data of userLeagueData) {
      if (data.league.type === 'Global') {
        this.updateGlobalCounts(data.status, counts);
      } else if (data.league.type === 'OneVsOne') {
        this.updateOneVsOneCounts(data.status, counts);
      }
    }
  }

  private updateGlobalCounts(status: string, counts: any) {
    switch (status) {
      case MatchStatus.WIN:
        counts.globalWinCount++;
        break;
      case MatchStatus.LOSS:
        counts.globalLossCount++;
        break;
      case MatchStatus.DRAW:
        counts.globalDrawCount++;
        break;
      case MatchStatus.PENDING:
        counts.globalCurrentlyPlaying++;
        break;
    }
  }

  private updateOneVsOneCounts(status: string, counts: any) {
    switch (status) {
      case MatchStatus.WIN:
        counts.oneVsOneWinCount++;
        break;
      case MatchStatus.LOSS:
        counts.oneVsOneLossCount++;
        break;
      case MatchStatus.DRAW:
        counts.oneVsOneDrawCount++;
        break;
      case MatchStatus.PENDING:
        counts.oneVsOneCurrentlyPlaying++;
        break;
    }
  }

  async getUserOneVsOneLeagues(userId: string): Promise<OneVsOneLeagueResult[]> {
    if (!userId) {
      throw new BadRequestException('Invalid user ID');
    }

    // Check if user exists
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // First, get all 1v1 leagues
    const oneVsOneLeagues = await this.leagueRepository
      .createQueryBuilder('league')
      .leftJoinAndSelect('league.premierLeague', 'premierLeague')
      .where('league.type = :leagueType', { leagueType: 'OneVsOne' })
      .getMany();

    const result: OneVsOneLeagueResult[] = [];

    for (const league of oneVsOneLeagues) {
      // Get user's team in this league
      const userTeam = await this.userTeamRepository
        .createQueryBuilder('userTeam')
        .leftJoinAndSelect('userTeam.user', 'user')
        .andWhere('userTeam.user.id = :userId', { userId })
        .getOne();

      if (!userTeam) continue; // Skip if user doesn't have a team in this league

      // Get user's league data
      const userLeagueData = await this.userLeagueDataRepository
        .createQueryBuilder('userLeagueData')
        .where('userLeagueData.league_id = :leagueKey', {
          leagueKey: league.league_key,
        })
        .andWhere('userLeagueData.user_team_id = :userTeamId', {
          userTeamId: userTeam.id,
        })
        .getOne();

      if (!userLeagueData) continue; // Skip if no league data found

      // Get opponent's league data
      const opponentLeagueData = await this.userLeagueDataRepository
        .createQueryBuilder('userLeagueData')
        .leftJoinAndSelect('userLeagueData.userTeam', 'userTeam')
        .leftJoinAndSelect('userTeam.user', 'user')
        .where('userLeagueData.league_id = :leagueKey', {
          leagueKey: league.league_key,
        })
        .andWhere('userLeagueData.user_team_id != :userTeamId', {
          userTeamId: userTeam.id,
        })
        .getOne();

      // Get scores for both teams for the specific week
      const userTeamScores = await this.userTeamScoreRepository
        .createQueryBuilder('score')
        .select('SUM(score.totalScore)', 'totalScore')
        .where('score.userTeamId = :userTeamId', { userTeamId: userTeam.id })
        .andWhere('score.gameWeek = :weekNumber', {
          weekNumber: league.week_number,
        })
        .getRawOne();

      const opponentTeamScores = opponentLeagueData
        ? await this.userTeamScoreRepository
            .createQueryBuilder('score')
            .select('SUM(score.totalScore)', 'totalScore')
            .where('score.userTeamId = :userTeamId', {
              userTeamId: opponentLeagueData.user_team_id,
            })
            .andWhere('score.gameWeek = :weekNumber', {
              weekNumber: league.week_number,
            })
            .getRawOne()
        : null;

      result.push({
        leagueName: league.description,
        premierLeagueName: league.premierLeague?.name,
        season: league.sesson,
        gameWeek: league.week_number,
        opponentName: opponentLeagueData
          ? `${opponentLeagueData.userTeam.user.firstName} ${opponentLeagueData.userTeam.user.lastName}`
          : 'No opponent',
        result: userLeagueData.status || MatchStatus.PENDING,
        scores: {
          user: [
            {
              gameWeek: league.week_number,
              totalScore: parseFloat(userTeamScores?.totalScore || '0'),
            },
          ],
          opponent: opponentTeamScores
            ? [
                {
                  gameWeek: league.week_number,
                  totalScore: parseFloat(opponentTeamScores.totalScore || '0'),
                },
              ]
            : [],
        },
      });
    }

    return result;
  }

  async getUserGlobalLeaderboard(userId: string) {
    // Get all user teams with their leagues
    const userTeams = await this.userTeamRepository
      .createQueryBuilder('userTeam')
      .leftJoinAndSelect('userTeam.league', 'league')
      .leftJoinAndSelect('league.premierLeague', 'premierLeague')
      .where('userTeam.user.id = :userId', { userId })
      .getMany();

    if (!userTeams.length) {
      return {
        totalScore: 0,
        rank: 0,
        matchesPlayed: 0,
        wins: 0,
        leagues: [],
      };
    }

    // Get all league IDs where user has teams
    const userLeagueIds = userTeams.map((team) => team.league.id);

    // Get all teams in the same leagues as user's teams
    const relevantTeams = await this.userTeamRepository
      .createQueryBuilder('userTeam')
      .leftJoinAndSelect('userTeam.user', 'user')
      .where('userTeam.league.id IN (:...leagueIds)', {
        leagueIds: userLeagueIds,
      })
      .getMany();

    // Get total scores for all relevant teams
    const teamScores = await Promise.all(
      relevantTeams.map(async (team) => {
        const totalScore = await this.userTeamScoreRepository
          .createQueryBuilder('score')
          .where('score.userTeamId = :userTeamId', { userTeamId: team.id })
          .select('SUM(score.totalScore)', 'total')
          .getRawOne();
        return {
          teamId: team.id,
          userId: team.user.id,
          totalScore: parseFloat(totalScore?.total || '0'),
        };
      }),
    );

    // Sort teams by total score
    teamScores.sort((a, b) => b.totalScore - a.totalScore);

    // Calculate user's total score and rank
    const userTotalScore = teamScores
      .filter((score) => score.userId === userId)
      .reduce((sum, score) => sum + score.totalScore, 0);

    const rank = teamScores.findIndex((score) => score.userId === userId) + 1;

    const leagues = await Promise.all(
      userTeams.map(async (team) => {
        // Get match statistics
        const userLeagueData = await this.userLeagueDataRepository
          .createQueryBuilder('userLeagueData')
          .where('userLeagueData.user_team_id = :userTeamId', {
            userTeamId: team.id,
          })
          .andWhere('userLeagueData.league_id = :leagueKey', {
            leagueKey: team.league.league_key,
          })
          .getMany();

        const matchStats = userLeagueData.reduce(
          (acc, data) => {
            if (data.status === MatchStatus.WIN) acc.wins++;
            else if (data.status === MatchStatus.LOSS) acc.losses++;
            else if (data.status === MatchStatus.DRAW) acc.draws++;
            return acc;
          },
          { wins: 0, losses: 0, draws: 0 },
        );

        return {
          leagueId: team.league.league_key,
          leagueName: team.league.premierLeague?.name || team.league.description,
          totalScore: teamScores.find((s) => s.teamId === team.id)?.totalScore || 0,
          matchesPlayed: userLeagueData.length,
          ...matchStats,
        };
      }),
    );

    // Calculate total matches played and wins from leagues array
    const totalStats = leagues.reduce(
      (acc, league) => {
        acc.matchesPlayed += league.matchesPlayed;
        acc.wins += league.wins;
        return acc;
      },
      { matchesPlayed: 0, wins: 0 },
    );

    return {
      totalScore: userTotalScore,
      rank,
      matchesPlayed: totalStats.matchesPlayed,
      wins: totalStats.wins,
    };
  }

  /**
   * Connects a wallet to a user account
   *
   * @param userId - The ID of the user to connect the wallet to
   * @param connectWalletDto - DTO containing wallet address and signature
   * @returns The connected wallet entity
   * @throws ConflictException - If the wallet is already connected to another user
   * @throws InternalServerErrorException - If there's an error during the process
   */
  async connectWallet(userId: string, connectWalletDto: ConnectWalletDto): Promise<Wallet> {
    const { address, signature } = connectWalletDto;

    // Fetch the user by ID
    const user = await this.getUserById(userId);

    try {
      // Check if the wallet address already exists in the database
      const existingWallet = await this.walletRepository.findOne({
        where: { address },
        relations: ['user'],
      });

      // If wallet exists and belongs to the same user, return it
      if (existingWallet) {
        if (existingWallet.user.id === userId) {
          return existingWallet;
        } else {
          // If wallet exists but belongs to a different user, throw conflict error
          throw new ConflictException('Wallet address already connected to another user');
        }
      }

      // Create a new wallet entity and associate it with the user
      let wallet = this.walletRepository.create({
        address,
        signature,
        user,
      });

      // Save the new wallet to the database
      wallet = await this.walletRepository.save(wallet);

      // Return the newly created wallet
      return wallet;
    } catch (err) {
      // Log the error for debugging
      this.logger.error(err.message, err.stack, UserService.name);
      // Handle unique constraint violation (example: duplicate email)
      if (err.code === '23505') throw new ConflictException('Email already exists');
      else throw new InternalServerErrorException(err.message);
    }
  }

  /**
   * Retrieves all wallets associated with a user
   *
   * @param userId - The ID of the user whose wallets to retrieve
   * @returns Array of wallet entities belonging to the user
   * @throws NotFoundException - If no wallets are found for the user
   */
  async getUserWallets(userId: string): Promise<Wallet[]> {
    const user = await this.getUserById(userId, true);

    console.log(user?.wallets);

    if (!user.wallets.length) throw new NotFoundException('No wallets found for user');

    return user.wallets;
  }

  /**
   * Retrieves a user by their ID
   *
   * @param userId - The ID of the user to retrieve
   * @param loadRelations - Whether to load related entities (e.g., wallets)
   * @returns The user entity
   * @throws NotFoundException - If the user is not found
   */
  async getUserById(userId: string, loadRelations: boolean = false): Promise<User> {
    // Get user details
    const user = await this.userRepository.findOne({
      where: { id: userId },
      relations: loadRelations ? ['wallets'] : [],
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    return user;
  }

  /**
   * Updates a user's profile information
   *
   * @param userId - The ID of the user whose profile to update
   * @param updateData - DTO containing the fields to update
   * @returns The updated user entity
   * @throws NotFoundException - If the user is not found
   */
  async updateUserProfile(userId: string, updateData: UpdateProfileDto): Promise<User> {
    const { firstName, lastName, bio, imageKey } = updateData;

    // Get user details
    let user = await this.getUserById(userId);

    // Update only provided fields
    if (firstName !== undefined) user.firstName = firstName;
    if (lastName !== undefined) user.lastName = lastName;
    if (bio !== undefined) user.bio = bio;
    if (imageKey !== undefined) {
      // Delete the existing image if new image is uploaded
      if (user.profileImageKey) {
        await this.s3Service.deleteObject(user.profileImageKey);
      }
      user.profileImageKey = imageKey;
    }

    // Save updated user
    user = await this.userRepository.save(user);

    return user;
  }
}
