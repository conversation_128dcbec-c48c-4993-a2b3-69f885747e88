import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserProfileController } from './controllers/user-profile.controller';
import { UserProfileService } from './services/user-profile.service';
import { UserLeagueData } from '../leagues/entities/user-league-data.entity';
import { Wallet } from '../wallet/entities/wallet.entity';
import { User } from './entities/user.entity';
import { UserTeam } from '../leagues/entities/user-team.entity';
import { UserTeamScore } from '../leagues/entities/user-team-score.entity';
import { League } from '../leagues/entities/league.entity';
import { PremierLeague } from '../premier-league/entities/premier-league.entity';
import { FileUploadService } from './services/file-upload.service';
import { AwsConfigService } from '../config/aws.config';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserLeagueData,
      Wallet,
      User,
      UserTeam,
      UserTeamScore,
      League,
      PremierLeague,
    ]),
  ],
  controllers: [UserProfileController],
  providers: [UserProfileService, FileUploadService, AwsConfigService],
  exports: [UserProfileService],
})
export class UserProfileModule {} 