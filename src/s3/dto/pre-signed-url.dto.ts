import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  Max,
  <PERSON><PERSON><PERSON>th,
  <PERSON>,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { PrimaryPaths } from '../enums/primary-path.enum';
import { Transform } from 'class-transformer';

export class PreSignedUrlDTO {
  @ApiProperty({
    required: true,
    description: 'upload file name',
    example: 'my-image.png',
  })
  @MaxLength(50)
  @MinLength(1)
  @IsString()
  @IsNotEmpty()
  fileName: string;

  @ApiProperty({
    required: true,
    description: 'Primary path name of the files',
    enum: PrimaryPaths,
    example: PrimaryPaths.USER_UPLOADS,
  })
  @IsEnum(PrimaryPaths, {
    message: `primaryPath must be one of the following values: ${Object.values(PrimaryPaths).join(', ')}`,
  })
  @IsString()
  @IsNotEmpty()
  primaryPath: PrimaryPaths;

  @ApiPropertyOptional({
    description: 'no of secs for which the s3 url should be live',
    example: 300,
  })
  @Max(900, { message: 'URL can be live for maximum 15 mins or 900 secs' })
  @Min(60, { message: 'URL must be live for 60 secs' })
  @IsPositive()
  @IsInt()
  @IsNumber()
  @IsOptional()
  @Transform(({ value }) => +value)
  expiresIn?: number;
}
