import { Controller, Post, Body, UseGuards, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiResponse } from '@nestjs/swagger';
import { PointsConfigService } from './points-config.service';
import { PointsConfigDto } from './dto/points-config.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../auth/guards/admin.guard';

@ApiTags('Config')
@ApiBearerAuth('JWT-auth')
@Controller('config/points')
export class PointsConfigController {
  constructor(private readonly pointsConfigService: PointsConfigService) {}

  @Post()
  @ApiOperation({ summary: 'Create or update the points config (admin only)' })
  @ApiResponse({ status: 201, description: 'Config created/updated successfully' })
  @UseGuards(JwtAuthGuard, AdminGuard)
  async createOrUpdate(@Body() dto: PointsConfigDto) {
    return this.pointsConfigService.createOrUpdateConfig(dto);
  }

  @Get()
  @ApiOperation({ summary: 'Get the current points config (admin only)' })
  @ApiResponse({ status: 200, description: 'Current config returned successfully' })
  @UseGuards(JwtAuthGuard, AdminGuard)
  async getConfig() {
    return this.pointsConfigService.getConfig();
  }
} 