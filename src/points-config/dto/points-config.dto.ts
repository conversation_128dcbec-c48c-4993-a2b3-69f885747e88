import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class ActionPointsDto {
  @ApiProperty({ example: 'play_<=60', description: 'The action identifier' })
  @IsString()
  action: string;

  @ApiProperty({ example: 1, description: 'Points for this action' })
  @IsNumber()
  @Min(0)
  points: number;
}

export class PointsConfigDto {
  @ApiProperty({ type: [ActionPointsDto], description: 'Array of action points' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ActionPointsDto)
  actions: ActionPointsDto[];
} 