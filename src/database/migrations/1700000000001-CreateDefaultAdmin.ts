// import { MigrationInterface, QueryRunner } from "typeorm";

// export class CreateDefaultAdmin1700000000001 implements MigrationInterface {
//     name = 'CreateDefaultAdmin1700000000001'

//     public async up(queryRunner: QueryRunner): Promise<void> {
//         // Insert default admin user with hashed password (admin123)
//         await queryRunner.query(`
//             INSERT INTO users (email, password, "firstName", "lastName", role, "createdAt", "updatedAt")
//             VALUES (
//                 '<EMAIL>',
//                 '$2b$10$3euPcmQFCiblsZeEu5s7p.9BU9F8jQ8YQN9hP6q9Qxq9Qxq9Qxq9Q', -- hashed 'admin123'
//                 'Admin',
//                 'User',
//                 'admin',
//                 NOW(),
//                 NOW()
//             )
//             ON CONFLICT (email) DO NOTHING;
//         `);
//     }

//     public async down(queryRunner: QueryRunner): Promise<void> {
//         await queryRunner.query(`
//             DELETE FROM users WHERE email = '<EMAIL>';
//         `);
//     }
// } 