import {
  Controller,
  Post,
  Get,
  Body,
  UseGuards,
  Request,
  Param,
  ParseIntPipe,
  Patch,
  Put,
  ParseUUIDPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
  ApiParam,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { UserTeamService } from './user-team.service';
import { CreateUserTeamDto } from './dto/create-user-team.dto';
import { UserTeam } from './entities/user-team.entity';
import { UpdatePlayerDto } from './dto/update-player.dto';
import { UpdateTeamDto } from './dto/update-team.dto';

@ApiTags('User Teams')
@Controller('user-teams')
export class UserTeamController {
  constructor(private readonly userTeamService: UserTeamService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Create a new user team' })
  @ApiResponse({
    status: 201,
    description: 'The user team has been successfully created.',
    type: UserTeam,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid team composition.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - User or League not found.',
  })
  async create(
    @Request() req,
    @Body() createUserTeamDto: CreateUserTeamDto,
  ): Promise<UserTeam> {
    return this.userTeamService.create(req.user.id, createUserTeamDto);
  }

  @Get('league/:leagueId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get user team for a specific league' })
  @ApiParam({
    name: 'leagueId',
    description: 'The ID of the league',
    type: Number,
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the user team for the specified league.',
    type: UserTeam,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({
    status: 404,
    description: 'Not Found - User team not found for this league.',
  })
  async getUserTeam(
    @Request() req,
    @Param('leagueId', ParseIntPipe) leagueId: number,
  ): Promise<UserTeam> {
    return this.userTeamService.getUserTeam(req.user.id, leagueId);
  }

  @Get('all')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get all teams for the authenticated user' })
  @ApiResponse({
    status: 200,
    description: 'Returns all teams for the authenticated user.',
    type: [UserTeam],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getAllTeamsByUser(@Request() req): Promise<UserTeam[]> {
    return this.userTeamService.getAllTeamsByUser(req.user.id);
  }

  @Get(':teamId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get a team by ID' })
  @ApiParam({
    name: 'teamId',
    description: 'The ID of the team',
    type: Number,
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the team with the specified ID.',
    type: UserTeam,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Not Found - Team not found.' })
  async getTeamById(
    @Param('teamId', ParseIntPipe) teamId: number,
  ): Promise<UserTeam> {
    return this.userTeamService.getTeamById(teamId);
  }

  @Get('leaderboard/:leagueId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get leaderboard for a specific league',
    description:
      'Retrieves the leaderboard for a specific league, including team details, total price, total score, and detailed last gameweek points with all fixtures in that gameweek, sorted by total score in descending order.',
  })
  @ApiParam({
    name: 'leagueId',
    description: 'The ID of the league',
    type: 'string',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the leaderboard for the specified league.',
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - User is not authenticated.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - League not found.',
  })
  async getLeaderboard(@Param('leagueId') leagueId: string) {
    return this.userTeamService.getLeaderboard(leagueId);
  }

  @Get(':userId/leagues')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary:
      'Get all Premier Leagues for a given user ID, with league name, status, position, and total score.',
  })
  @ApiParam({
    name: 'userId',
    type: Number,
    required: true,
    description: 'User ID',
  })
  @ApiResponse({
    status: 200,
    description:
      'Returns all Premier Leagues for the user with league name, status, position, and total score.',
    type: [Object],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getPremierLeaguesByUserById(
    @Param('userId', ParseUUIDPipe) userId: string,
  ) {
    return this.userTeamService.getPremierLeaguesByUser(userId);
  }

  @Patch(':teamId/players')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Update a player in the team' })
  @ApiResponse({ status: 200, description: 'Player updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 404, description: 'Team or player not found' })
  async updatePlayer(
    @Request() req,
    @Param('teamId') teamId: number,
    @Body() updatePlayerDto: UpdatePlayerDto,
  ): Promise<UserTeam> {
    return this.userTeamService.updatePlayer(
      req.user.id,
      teamId,
      updatePlayerDto,
    );
  }

  @Get(':teamId/transferWindow')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Check if a player can be updated for the team' })
  @ApiResponse({
    status: 200,
    description: 'Returns whether a player can be updated',
  })
  @ApiResponse({ status: 404, description: 'Team not found' })
  async transferWindow(
    @Request() req,
    @Param('teamId') teamId: number,
  ): Promise<{ canUpdate: boolean }> {
    const canUpdate = await this.userTeamService.canUpdatePlayer(teamId);
    return { canUpdate };
  }

  @Put(':teamId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Update an entire team' })
  @ApiParam({
    name: 'teamId',
    description: 'The ID of the team to update',
    type: Number,
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'The team has been successfully updated.',
    type: UserTeam,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid team composition.',
  })
  @ApiResponse({ status: 404, description: 'Not Found - Team not found.' })
  async updateTeam(
    @Request() req,
    @Param('teamId', ParseIntPipe) teamId: number,
    @Body() updateTeamDto: UpdateTeamDto,
  ): Promise<UserTeam> {
    return this.userTeamService.updateTeam(req.user.id, teamId, updateTeamDto);
  }
}
