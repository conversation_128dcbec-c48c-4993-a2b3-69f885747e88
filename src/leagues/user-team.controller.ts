import { Controller, Post, Get, Body, UseGuards, Request, Param, ParseIntPipe } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { UserTeamService } from './user-team.service';
import { CreateUserTeamDto } from './dto/create-user-team.dto';
import { UserTeam } from './entities/user-team.entity';

@ApiTags('User Teams')
@Controller('user-teams')
export class UserTeamController {
  constructor(private readonly userTeamService: UserTeamService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Create a new user team' })
  @ApiResponse({
    status: 201,
    description: 'The user team has been successfully created.',
    type: UserTeam,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 400, description: 'Bad Request - Invalid team composition.' })
  @ApiResponse({ status: 404, description: 'Not Found - User or League not found.' })
  async create(@Request() req, @Body() createUserTeamDto: CreateUserTeamDto): Promise<UserTeam> {
    return this.userTeamService.create(req.user.id, createUserTeamDto);
  }

  @Get('league/:leagueId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Get user team for a specific league' })
  @ApiParam({
    name: 'leagueId',
    description: 'The ID of the league',
    type: String,
    required: true
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the user team for the specified league.',
    type: UserTeam,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Not Found - User team not found for this league.' })
  async getUserTeam(
    @Request() req,
    @Param('leagueId') leagueId: string
  ): Promise<UserTeam> {
    return this.userTeamService.getUserTeam(req.user.id, leagueId);
  }
} 