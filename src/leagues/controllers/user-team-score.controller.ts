import { Controller, Get, Param, <PERSON>se, MessageEvent, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiResponse, ApiParam, ApiExtraModels } from '@nestjs/swagger';
import { Observable, interval, from } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { UserTeamScoreService } from '../services/user-team-score.service';
import { UserTeamScore } from '../entities/user-team-score.entity';

@ApiTags('User Team Scores')
@Controller('user-team-scores')
@ApiExtraModels(UserTeamScore)
export class UserTeamScoreController {
  constructor(private readonly userTeamScoreService: UserTeamScoreService) {}

  @Get(':userTeamId/league/:leagueId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Get user team score for a specific league',
    description: 'Retrieves the current score and detailed statistics for a user\'s team in a specific league.'
  })
  @ApiParam({
    name: 'userTeamId',
    description: 'The ID of the user\'s team',
    type: 'number',
    required: true
  })
  @ApiParam({
    name: 'leagueId',
    description: 'The ID of the league',
    type: 'string',
    required: true
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the user team score with detailed player statistics.',
    type: UserTeamScore
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - User is not authenticated.'
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Team score not found for the specified team and league.'
  })
  async getTeamScore(
    @Param('userTeamId') userTeamId: number,
    @Param('leagueId') leagueId: string,
  ): Promise<UserTeamScore> {
    return this.userTeamScoreService.getTeamScore(userTeamId, leagueId);
  }

  @Sse(':userTeamId/league/:leagueId/stream')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({
    summary: 'Stream real-time updates of user team score',
    description: 'Establishes a Server-Sent Events (SSE) connection to receive real-time updates of the team\'s score and player statistics. Updates are sent every 30 seconds.'
  })
  @ApiParam({
    name: 'userTeamId',
    description: 'The ID of the user\'s team',
    type: 'number',
    required: true
  })
  @ApiParam({
    name: 'leagueId',
    description: 'The ID of the league',
    type: 'string',
    required: true
  })
  @ApiResponse({
    status: 200,
    description: 'Establishes an SSE connection that streams score updates.',
    content: {
      'text/event-stream': {
        schema: {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              properties: {
                id: { type: 'number' },
                userTeamId: { type: 'number' },
                leagueId: { type: 'string' },
                totalScore: { type: 'number' },
                playerScores: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      playerId: { type: 'number' },
                      score: { type: 'number' },
                      actions: {
                        type: 'array',
                        items: {
                          type: 'object',
                          properties: {
                            action: { type: 'string' },
                            points: { type: 'number' },
                            timestamp: { type: 'string', format: 'date-time' }
                          }
                        }
                      }
                    }
                  }
                },
                lastUpdated: { type: 'string', format: 'date-time' }
              }
            },
            id: { type: 'string' },
            type: { type: 'string' }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - User is not authenticated.'
  })
  streamTeamScore(
    @Param('userTeamId') userTeamId: number,
    @Param('leagueId') leagueId: string,
  ): Observable<MessageEvent> {
    return interval(30000).pipe( // Update every 30 seconds
      switchMap(() => from(this.userTeamScoreService.calculateTeamScore(userTeamId, leagueId))),
      map(score => ({
        data: score,
        id: Date.now().toString(),
        type: 'score_update',
      })),
    );
  }
} 