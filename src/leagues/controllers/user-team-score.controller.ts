import {
  Controller,
  Get,
  Param,
  <PERSON>se,
  MessageEvent,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiBearerAuth,
  ApiResponse,
  ApiParam,
  ApiExtraModels,
} from '@nestjs/swagger';
import { Observable, interval, from, timer, of } from 'rxjs';
import { map, startWith, switchMap, retry, catchError } from 'rxjs/operators';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { UserTeamScoreService } from '../services/user-team-score.service';
import { UserTeamScore } from '../entities/user-team-score.entity';

@ApiTags('User Team Scores')
@Controller('user-team-scores')
@ApiExtraModels(UserTeamScore)
@ApiBearerAuth('JWT-auth')
export class UserTeamScoreController {
  constructor(private readonly userTeamScoreService: UserTeamScoreService) {}

  @Get('/teams/:userTeamId/game-weeks/:gameWeek')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get user team score for a specific game week',
    description: "Retrieves the current score and detailed statistics for a user's team in a specific game week.",
  })
  @ApiParam({
    name: 'userTeamId',
    description: "The ID of the user's team",
    type: 'number',
    required: true,
  })
  @ApiParam({
    name: 'gameWeek',
    description: 'The game week number',
    type: 'number',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the user team score with detailed player statistics for the game week.',
    schema: {
      type: 'object',
      properties: {
        totalScore: { type: 'number' },
        playerScores: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              playerId: { type: 'number' },
              playerName: { type: 'string' },
              playerPhoto: { type: 'string' },
              totalScore: { type: 'number' },
              actions: {
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    action: { type: 'string' },
                    points: { type: 'number' },
                    timestamp: { type: 'string', format: 'date-time' },
                  },
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - User is not authenticated.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Team score not found for the specified team and game week.',
  })
  async getTeamScoreByGameWeek(
    @Param('userTeamId') userTeamId: number,
    @Param('gameWeek') gameWeek: number,
  ) {
    return this.userTeamScoreService.getTeamScoreByGameWeek(userTeamId, gameWeek);
  }

  @Sse('/userTeamId/:userTeamId/gameWeek/:gameWeek')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Stream real-time updates of team score for a specific game week',
    description: 'Establishes a Server-Sent Events (SSE) connection to receive real-time updates of the team\'s score and player statistics for a specific game week. Updates are sent every 15 seconds.',
  })
  @ApiParam({
    name: 'userTeamId',
    description: "The ID of the user's team",
    type: 'number',
    required: true,
  })
  @ApiParam({
    name: 'gameWeek',
    description: 'The game week number',
    type: 'number',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Establishes an SSE connection that streams score updates for the specified game week.',
    content: {
      'text/event-stream': {
        schema: {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              properties: {
                totalScore: { type: 'number' },
                playerScores: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      playerId: { type: 'number' },
                      playerName: { type: 'string' },
                      playerPhoto: { type: 'string' },
                      totalScore: { type: 'number' },
                      actions: {
                        type: 'array',
                        items: {
                          type: 'object',
                          properties: {
                            action: { type: 'string' },
                            points: { type: 'number' },
                            timestamp: { type: 'string', format: 'date-time' },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
            id: { type: 'string' },
            type: { type: 'string' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - User is not authenticated.',
  })
  @ApiResponse({
    status: 404,
    description: 'Not Found - Team score not found for the specified team and game week.',
  })
  streamTeamScoreByGameWeek(
    @Param('userTeamId') userTeamId: number,
    @Param('gameWeek') gameWeek: number,
  ): Observable<MessageEvent> {
    return interval(30000).pipe(
      startWith(0),
      switchMap(() => {
        return from(
          this.userTeamScoreService.getTeamScoreByGameWeek(userTeamId, gameWeek),
        ).pipe(
          retry({
            count: 3,
            delay: (error, retryCount) => {
              return timer(Math.min(1000 * Math.pow(2, retryCount), 10000));
            },
          }),
          catchError((error) => {
            console.error('[SSE] Error fetching team score:', error);
            return of({
              data: {
                error: 'Failed to fetch team score',
                timestamp: new Date().toISOString(),
              },
              id: Date.now().toString(),
              type: 'error',
            });
          }),
        );
      }),
      map((score) => {
        return {
          data: score,
          id: Date.now().toString(),
          type: 'game_week_score_update',
        };
      }),
    );
  }

  @Sse(':userTeamId/global-fixture-scores')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Stream real-time updates of global team scores across all fixtures',
    description: 'Establishes a Server-Sent Events (SSE) connection to receive real-time updates of the team\'s total score and player statistics across all fixtures. Updates are sent every 30 seconds.',
  })
  @ApiParam({
    name: 'userTeamId',
    description: "The ID of the user's team",
    type: 'number',
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: 'Establishes an SSE connection that streams global score updates.',
    content: {
      'text/event-stream': {
        schema: {
          type: 'object',
          properties: {
            data: {
              type: 'object',
              properties: {
                totalScore: { type: 'number' },
                playerScores: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      playerId: { type: 'number' },
                      totalScore: { type: 'number' },
                      playerName: { type: 'string' },
                      playerPhoto: { type: 'string' },
                      actions: {
                        type: 'array',
                        items: {
                          type: 'object',
                          properties: {
                            action: { type: 'string' },
                            points: { type: 'number' },
                            timestamp: { type: 'string', format: 'date-time' },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
            id: { type: 'string' },
            type: { type: 'string' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - User is not authenticated.',
  })
  streamGlobalTeamScoreByFixture(
    @Param('userTeamId') userTeamId: number,
  ): Observable<MessageEvent> {
    console.log(`[SSE] Starting global stream for userTeamId: ${userTeamId}`);
    return interval(30000).pipe(
      startWith(0),
      switchMap(() => {
        return from(
          this.userTeamScoreService.calculateGlobalTeamScoreByFixture(
            userTeamId,
          ),
        ).pipe(
          retry({
            count: 3,
            delay: (error, retryCount) => {
              return timer(Math.min(1000 * Math.pow(2, retryCount), 10000));
            },
          }),
          catchError((error) => {
            return of({
              data: {
                error: 'Failed to fetch global team score',
                timestamp: new Date().toISOString(),
              },
              id: Date.now().toString(),
              type: 'error',
            });
          }),
        );
      }),
      map((score) => {
        return {
          data: score,
          id: Date.now().toString(),
          type: 'global_score_update',
        };
      }),
    );
  }
}
