import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserTeamScore } from '../entities/user-team-score.entity';
import { UserTeam } from '../entities/user-team.entity';
import { PremierLeaguePlayerStats } from '../../premier-league/entities/premier-league-player-stats.entity';
import { PointsConfig } from '../../points-config/entities/points-config.entity';
import { ApiProperty } from '@nestjs/swagger';
import { In } from 'typeorm';
import { PremierLeaguePlayers } from '../../premier-league/entities/premier-league-players.entity';
import { UserLeagueData } from '../entities/user-league-data.entity';

export interface PlayerAction {
  action: string;
  points: number;
  timestamp: Date;
}

@Injectable()
export class UserTeamScoreService {
  constructor(
    @InjectRepository(UserTeamScore)
    private readonly userTeamScoreRepository: Repository<UserTeamScore>,
    @InjectRepository(UserTeam)
    private readonly userTeamRepository: Repository<UserTeam>,
    @InjectRepository(PremierLeaguePlayerStats)
    private readonly playerStatsRepository: Repository<PremierLeaguePlayerStats>,
    @InjectRepository(PointsConfig)
    private readonly pointsConfigRepository: Repository<PointsConfig>,
    @InjectRepository(PremierLeaguePlayers)
    private readonly premierLeaguePlayersRepository: Repository<PremierLeaguePlayers>,
    @InjectRepository(UserLeagueData)
    private readonly userLeagueDataRepository: Repository<UserLeagueData>,
  ) {}


  async getTeamScoreByGameWeek(userTeamId: number, gameWeek: number): Promise<{
    totalScore: number;
    playerScores: { playerId: number; totalScore: number; actions: PlayerAction[]; playerName: string; playerPhoto: string }[];
  }> {    
    // Get all scores for this game week
    const teamScores = await this.userTeamScoreRepository.find({
      where: { 
        userTeamId,
        gameWeek
      }
    });

    if (teamScores.length === 0) {
      return {
        totalScore: 0,
        playerScores: []
      };
    }

    // Get points configuration to know all possible actions
    const pointsConfig = await this.pointsConfigRepository.find();
    const allPossibleActions = [
      ...pointsConfig.map(config => config.action),
      'captain_bonus',
      'vice_captain_bonus'
    ];

    // Calculate total score for the game week
    const totalScore = Number(teamScores.reduce((sum, score) => sum + Number(score.totalScore), 0).toFixed(2));

    // Combine player scores across all fixtures in the game week
    const playerScoresMap = new Map<number, { totalScore: number; actions: PlayerAction[] }>();

    teamScores.forEach(score => {
      score.playerScores.forEach(playerScore => {
        const existing = playerScoresMap.get(playerScore.playerId);
        if (existing) {
          existing.totalScore += playerScore.score;
          // Sum up action points for the same action type
          playerScore.actions.forEach(newAction => {
            const existingAction = existing.actions.find(a => a.action === newAction.action);
            if (existingAction) {
              existingAction.points += newAction.points;
            } else {
              existing.actions.push({ ...newAction });
            }
          });
        } else {
          playerScoresMap.set(playerScore.playerId, {
            totalScore: playerScore.score,
            actions: [...playerScore.actions]
          });
        }
      });
    });

    // Get player details
    const playerIds = Array.from(playerScoresMap.keys());
    const playerMap = await this.getPlayerDetails(playerIds);

    // Format player scores with all possible actions
    const playerScores = this.formatPlayerScores(playerScoresMap, playerMap, allPossibleActions);

    return {
      totalScore,
      playerScores
    };
  }

  private async calculatePlayerScore(
    player: any,
    stats: any,
    pointsMap: Map<string, number>,
    userTeam: any,
  ): Promise<{ playerId: number; score: number; actions: PlayerAction[] }> {
    const actions: PlayerAction[] = [];
    let playerScore = 0;
    const actionTimestamp = new Date();

    // Calculate minutes played points
    if (stats.minutes > 60) {
      const points = pointsMap.get('play_>60') || 0;
      playerScore += points;
      actions.push({
        action: 'play_>60',
        points,
        timestamp: actionTimestamp,
      });
    } else if (stats.minutes > 0) {
      const points = pointsMap.get('play_<=60') || 0;
      playerScore += points;
      actions.push({
        action: 'play_<=60',
        points,
        timestamp: actionTimestamp,
      });
    }

    // Calculate goals based on position
    if (stats.goalsTotal > 0) {
      let actionKey = 'goal_fwd';
      if (player.position.toLowerCase().includes('goalkeeper') || player.position.toLowerCase().includes('defender')) {
        actionKey = 'goal_gk_def';
      } else if (player.position.toLowerCase().includes('midfielder')) {
        actionKey = 'goal_mid';
      }

      const points = (pointsMap.get(actionKey) || 0) * stats.goalsTotal;
      playerScore += points;
      actions.push({
        action: actionKey,
        points,
        timestamp: actionTimestamp,
      });
    }

    // Calculate assists
    if (stats.goalsAssists > 0) {
      const points = (pointsMap.get('assist') || 0) * stats.goalsAssists;
      playerScore += points;
      actions.push({
        action: 'assist',
        points,
        timestamp: actionTimestamp,
      });
    }

    // Calculate saves for goalkeepers
    if (player.position.toLowerCase().includes('goalkeeper') && stats.goalsSaves > 0) {
      const points = (pointsMap.get('save') || 0) * stats.goalsSaves;
      playerScore += points;
      actions.push({
        action: 'save',
        points,
        timestamp: actionTimestamp,
      });
    }

    // Calculate clean sheet points
    if (stats.goalsConceded === 0 && stats.minutes >= 60) {
      let actionKey = 'clean_sheet_fwd';
      if (player.position.toLowerCase().includes('goalkeeper') || player.position.toLowerCase().includes('defender')) {
        actionKey = 'clean_sheet_gk_def';
      } else if (player.position.toLowerCase().includes('midfielder')) {
        actionKey = 'clean_sheet_mid';
      }

      const points = pointsMap.get(actionKey) || 0;
      playerScore += points;
      actions.push({
        action: actionKey,
        points,
        timestamp: actionTimestamp,
      });
    }

    // Apply captain/vice-captain multipliers
    if (userTeam.captain && userTeam.captain.playerId === player.playerId) {
      playerScore *= 2;
      actions.push({
        action: 'captain_bonus',
        points: playerScore / 2,
        timestamp: actionTimestamp,
      });
    } else if (userTeam.viceCaptain && userTeam.viceCaptain.playerId === player.playerId) {
      playerScore *= 1.5;
      actions.push({
        action: 'vice_captain_bonus',
        points: playerScore / 1.5,
        timestamp: actionTimestamp,
      });
    }

    return {
      playerId: player.playerId,
      score: playerScore,
      actions,
    };
  }

  private async getPlayerDetails(playerIds: number[]) {
    const players = await this.premierLeaguePlayersRepository.find({
      where: { playerId: In(playerIds) },
      select: ['playerId', 'name', 'photo']
    });
    return new Map(players.map(p => [p.playerId, p]));
  }

  private async calculateTotalScore(userTeamId: number) {
    const allScores = await this.userTeamScoreRepository.find({
      where: { userTeamId }
    });

    if (allScores.length === 0) {
      return {
        totalScore: 0,
        playerScoresMap: new Map<number, { totalScore: number; actions: PlayerAction[] }>()
      };
    }

    const totalScore = Number(allScores.reduce((sum, score) => sum + Number(score.totalScore), 0).toFixed(2));
    const playerScoresMap = new Map<number, { totalScore: number; actions: PlayerAction[] }>();

    allScores.forEach(score => {
      score.playerScores.forEach(playerScore => {
        const existing = playerScoresMap.get(playerScore.playerId);
        if (existing) {
          existing.totalScore += playerScore.score;
          // Sum up action points for the same action type
          playerScore.actions.forEach(newAction => {
            const existingAction = existing.actions.find(a => a.action === newAction.action);
            if (existingAction) {
              existingAction.points += newAction.points;
            } else {
              existing.actions.push({ ...newAction });
            }
          });
        } else {
          playerScoresMap.set(playerScore.playerId, {
            totalScore: playerScore.score,
            actions: [...playerScore.actions]
          });
        }
      });
    });

    return { totalScore, playerScoresMap };
  }

  private formatPlayerScores(
    playerScoresMap: Map<number, { totalScore: number; actions: PlayerAction[] }>,
    playerMap: Map<any, any>,
    allPossibleActions: string[]
  ) {
    return Array.from(playerScoresMap.entries()).map(([playerId, data]) => {
      const existingActionsMap = new Map(data.actions.map(action => [action.action, action]));
      const allActions = allPossibleActions.map(action => {
        const existingAction = existingActionsMap.get(action);
        return existingAction || {
          action,
          points: 0,
          timestamp: new Date()
        };
      });

      const playerDetails = playerMap.get(playerId);

      return {
        playerId,
        playerName: playerDetails?.name || '',
        playerPhoto: playerDetails?.photo || '',
        totalScore: Number(data.totalScore.toFixed(2)),
        actions: allActions
      };
    });
  }

  async calculateGlobalTeamScoreByFixture(userTeamId: number): Promise<{
    totalScore: number;
    playerScores: { playerId: number; totalScore: number; actions: PlayerAction[]; playerName: string; playerPhoto: string }[];
  }> {
    // Get user team with all players
    const userTeam = await this.userTeamRepository.findOne({
      where: { id: userTeamId },
      relations: ['goalkeepers', 'defenders', 'midfielders', 'forwards', 'captain', 'viceCaptain'],
      select: {
        id: true,
        captain: {
          id: true,
          playerId: true
        },
        viceCaptain: {
          id: true,
          playerId: true
        },
        goalkeeperSubstitutes: true,
        defenderSubstitutes: true,
        midfielderSubstitutes: true,
        forwardSubstitutes: true
      }
    });

    if (!userTeam) {
      throw new NotFoundException('User team not found');
    }

    // Get points configuration
    const pointsConfig = await this.pointsConfigRepository.find();
    const pointsMap = new Map(pointsConfig.map(config => [config.action, config.points]));
    const allPossibleActions = [
      ...pointsConfig.map(config => config.action),
      'captain_bonus',
      'vice_captain_bonus'
    ];

    // Get all players from the team with their substitute status
    const allPlayers = [
      ...userTeam.goalkeepers.map((player, index) => ({
        ...player,
        isSubstitute: userTeam.goalkeeperSubstitutes[index],
        positionGroup: 'goalkeeper'
      })),
      ...userTeam.defenders.map((player, index) => ({
        ...player,
        isSubstitute: userTeam.defenderSubstitutes[index],
        positionGroup: 'defender'
      })),
      ...userTeam.midfielders.map((player, index) => ({
        ...player,
        isSubstitute: userTeam.midfielderSubstitutes[index],
        positionGroup: 'midfielder'
      })),
      ...userTeam.forwards.map((player, index) => ({
        ...player,
        isSubstitute: userTeam.forwardSubstitutes[index],
        positionGroup: 'forward'
      }))
    ];

    // Get player IDs from the team
    const playerIds = allPlayers.map(player => player.playerId);

    // Get live fixtures where any of the team's players are present
    const fixtures = await this.playerStatsRepository
      .createQueryBuilder('stats')
      .leftJoinAndSelect('stats.fixture', 'fixture')
      .where('stats.playerId IN (:...playerIds)', { playerIds })
      .andWhere('fixture.status->>\'long\' IN (:...liveStatuses)', { liveStatuses: ['First Half', 'Halftime', 'Second Half', 'Extra Time', 'Penalty'] })
      .select('DISTINCT fixture.fixtureId', 'fixtureId')
      .getRawMany();

    const fixtureIds = fixtures.map(f => f.fixtureId);

    if (fixtureIds.length === 0) {
      const { totalScore, playerScoresMap } = await this.calculateTotalScore(userTeamId);
      if (totalScore === 0) {
        return { totalScore: 0, playerScores: [] };
      }

      const playerMap = await this.getPlayerDetails(Array.from(playerScoresMap.keys()));
      const playerScores = this.formatPlayerScores(playerScoresMap, playerMap, allPossibleActions);

      return { totalScore, playerScores };
    }

    // Create a map to store aggregated player scores across all fixtures
    const aggregatedPlayerScores = new Map<number, { score: number; actions: PlayerAction[] }>();
    // Track processed player-fixture combinations to prevent duplicates
    const processedPlayerFixtures = new Set<string>();

    // Calculate scores for each fixture
    await Promise.all(fixtureIds.map(async (fixtureId) => {
      // Track used substitutes for this fixture
      const usedSubstitutes = new Set<number>();

      const playerScores = await Promise.all(
        allPlayers.map(async (player) => {
          // Create a unique key for this player-fixture combination
          const playerFixtureKey = `${player.playerId}_${fixtureId}`;
          
          // Skip if we've already processed this player for this fixture
          if (processedPlayerFixtures.has(playerFixtureKey)) {
            return null;
          }

          // Skip substitute players initially
          if (player.isSubstitute) {
            return null;
          }

          const stats = await this.playerStatsRepository.findOne({
            where: { 
              playerId: player.playerId,
              fixtureId
            },
            order: { id: 'DESC' },
          });

          // If non-substitute player is not playing, check for available substitutes
          if (stats?.minutes === null) {
            // Get all unused substitute players for this position group
            const availableSubstitutes = allPlayers.filter(p => 
              p.isSubstitute && 
              p.positionGroup === player.positionGroup &&
              !usedSubstitutes.has(p.playerId)
            );

            // Try each available substitute in order until we find one that's playing
            for (const substitutePlayer of availableSubstitutes) {
              const substituteStats = await this.playerStatsRepository.findOne({
                where: { 
                  playerId: substitutePlayer.playerId,
                  fixtureId
                },
                order: { id: 'DESC' },
              });

              if (substituteStats && substituteStats.minutes > 0) {
                // Mark this substitute as used
                usedSubstitutes.add(substitutePlayer.playerId);
                // Mark this player-fixture combination as processed
                processedPlayerFixtures.add(`${substitutePlayer.playerId}_${fixtureId}`);
                return this.calculatePlayerScore(substitutePlayer, substituteStats, pointsMap, userTeam);
              }
            }
            return null;
          }

          // Mark this player-fixture combination as processed
          processedPlayerFixtures.add(playerFixtureKey);
          return this.calculatePlayerScore(player, stats, pointsMap, userTeam);
        })
      );

      // Filter out null scores and aggregate them
      const validPlayerScores = playerScores.filter(score => score !== null);
      
      // Deduplicate player scores by keeping only the latest entry for each player
      const deduplicatedPlayerScores = Array.from(
        validPlayerScores.reduce((map, score) => {
          const existing = map.get(score.playerId);
          if (!existing || score.actions[0].timestamp > existing.actions[0].timestamp) {
            map.set(score.playerId, score);
          }
          return map;
        }, new Map<number, any>()).values()
      );
      
      // Aggregate scores into the map
      deduplicatedPlayerScores.forEach(playerScore => {
        const existing = aggregatedPlayerScores.get(playerScore.playerId);
        if (existing) {
          existing.score += playerScore.score;
          // Merge actions, combining points for the same action type
          playerScore.actions.forEach(newAction => {
            const existingAction = existing.actions.find(a => a.action === newAction.action);
            if (existingAction) {
              existingAction.points += newAction.points;
            } else {
              existing.actions.push({ ...newAction });
            }
          });
        } else {
          aggregatedPlayerScores.set(playerScore.playerId, {
            score: playerScore.score,
            actions: [...playerScore.actions]
          });
        }
      });

      const fixture = await this.playerStatsRepository.findOne({
        where: { fixtureId },
        relations: ['fixture'],
        select: {
          fixture: {
            round: true
          }
        }
      });

      // Create a unique key for this userTeamId and fixtureId combination
      const uniqueScoreKey = `${userTeamId}_${fixtureId}`;

      // Calculate total score for this fixture
      const fixtureTotalScore = deduplicatedPlayerScores.reduce((sum, player) => sum + player.score, 0);

      // First try to find an existing record using the unique key
      let teamScore = await this.userTeamScoreRepository.findOne({
        where: { 
          uniqueScoreKey
        }
      });

      // If no record exists, create a new one
      if (!teamScore) {
        teamScore = this.userTeamScoreRepository.create({
          userTeamId,
          fixtureId,
          uniqueScoreKey,
          gameWeek: fixture ? parseInt(fixture.fixture.round.split('-')[1].trim()) : undefined,
          totalScore: fixtureTotalScore,
          playerScores: deduplicatedPlayerScores,
          lastUpdated: new Date(),
        });
      } else {
        // Update existing record
        Object.assign(teamScore, {
          totalScore: fixtureTotalScore,
          playerScores: deduplicatedPlayerScores,
          lastUpdated: new Date(),
        });
      }

      try {
        // Use upsert to prevent duplicates
        await this.userTeamScoreRepository.save(teamScore, {
          reload: false // Don't reload the entity after save
        });
      } catch (error) {
        // If there's a unique constraint violation, try to update the existing record
        if (error.code === '23505') { // PostgreSQL unique violation code
          const existingScore = await this.userTeamScoreRepository.findOne({
            where: { uniqueScoreKey }
          });
          if (existingScore) {
            Object.assign(existingScore, {
              totalScore: fixtureTotalScore,
              playerScores: deduplicatedPlayerScores,
              lastUpdated: new Date(),
            });
            await this.userTeamScoreRepository.save(existingScore, {
              reload: false
            });
          }
        } else {
          throw error;
        }
      }
    }));

    // Convert aggregated scores to the required format
    const playerScoresMap = new Map<number, { totalScore: number; actions: PlayerAction[] }>();
    aggregatedPlayerScores.forEach((data, playerId) => {
      playerScoresMap.set(playerId, {
        totalScore: data.score,
        actions: data.actions
      });
    });

    const totalScore = Array.from(aggregatedPlayerScores.values()).reduce((sum, data) => sum + data.score, 0);
    const playerMap = await this.getPlayerDetails(Array.from(playerScoresMap.keys()));
    const playerScores = this.formatPlayerScores(playerScoresMap, playerMap, allPossibleActions);

    return { totalScore, playerScores };
  }
}

