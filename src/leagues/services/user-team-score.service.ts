import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserTeamScore } from '../entities/user-team-score.entity';
import { UserTeam } from '../entities/user-team.entity';
import { PremierLeaguePlayerStats } from '../../premier-league/entities/premier-league-player-stats.entity';
import { PointsConfig } from '../../config/points-config.entity';
import { ApiProperty } from '@nestjs/swagger';

export interface PlayerAction {
  action: string;
  points: number;
  timestamp: Date;
}

@Injectable()
export class UserTeamScoreService {
  constructor(
    @InjectRepository(UserTeamScore)
    private readonly userTeamScoreRepository: Repository<UserTeamScore>,
    @InjectRepository(UserTeam)
    private readonly userTeamRepository: Repository<UserTeam>,
    @InjectRepository(PremierLeaguePlayerStats)
    private readonly playerStatsRepository: Repository<PremierLeaguePlayerStats>,
    @InjectRepository(PointsConfig)
    private readonly pointsConfigRepository: Repository<PointsConfig>,
  ) {}

  /**
   * Calculates the total score for a user's team in a specific league
   * @param userTeamId - The ID of the user's team
   * @param leagueId - The ID of the league
   * @returns The calculated team score with detailed player scores
   * @throws NotFoundException if the user team is not found
   */
  async calculateTeamScore(userTeamId: number, leagueId: string): Promise<UserTeamScore> {
    // Get user team with all players
    const userTeam = await this.userTeamRepository.findOne({
      where: { id: userTeamId },
      relations: ['goalkeepers', 'defenders', 'midfielders', 'forwards'],
    });

    if (!userTeam) {
      throw new NotFoundException('User team not found');
    }

    // Get points configuration
    const pointsConfig = await this.pointsConfigRepository.find();
    const pointsMap = new Map(pointsConfig.map(config => [config.action, config.points]));

    // Get all players from the team
    const allPlayers = [
      ...userTeam.goalkeepers,
      ...userTeam.defenders,
      ...userTeam.midfielders,
      ...userTeam.forwards,
    ];

    // Get latest stats for all players
    const playerScores = await Promise.all(
      allPlayers.map(async (player) => {
        const stats = await this.playerStatsRepository.findOne({
          where: { playerId: player.playerId },
          order: { id: 'DESC' },
        });

        if (!stats) {
          return {
            playerId: player.playerId,
            score: 0,
            actions: [] as PlayerAction[],
          };
        }

        const actions: PlayerAction[] = [];
        let playerScore = 0;

        // Calculate minutes played points
        if (stats.minutes > 60) {
          const points = pointsMap.get('play_>60') || 0;
          playerScore += points;
          actions.push({
            action: 'play_>60',
            points,
            timestamp: new Date(),
          });
        } else if (stats.minutes > 0) {
          const points = pointsMap.get('play_<=60') || 0;
          playerScore += points;
          actions.push({
            action: 'play_<=60',
            points,
            timestamp: new Date(),
          });
        }

        // Calculate goals based on position
        if (stats.goalsTotal > 0) {
          let actionKey = 'goal_fwd';
          if (player.position.toLowerCase().includes('goalkeeper') || player.position.toLowerCase().includes('defender')) {
            actionKey = 'goal_gk_def';
          } else if (player.position.toLowerCase().includes('midfielder')) {
            actionKey = 'goal_mid';
          }

          const points = (pointsMap.get(actionKey) || 0) * stats.goalsTotal;
          playerScore += points;
          actions.push({
            action: actionKey,
            points,
            timestamp: new Date(),
          });
        }

        // Calculate assists
        if (stats.goalsAssists > 0) {
          const points = (pointsMap.get('assist') || 0) * stats.goalsAssists;
          playerScore += points;
          actions.push({
            action: 'assist',
            points,
            timestamp: new Date(),
          });
        }

        // Calculate saves for goalkeepers
        if (player.position.toLowerCase().includes('goalkeeper') && stats.goalsSaves > 0) {
          const points = (pointsMap.get('save') || 0) * stats.goalsSaves;
          playerScore += points;
          actions.push({
            action: 'save',
            points,
            timestamp: new Date(),
          });
        }

        // Calculate clean sheet points
        if (stats.goalsConceded === 0 && stats.minutes >= 60) {
          let actionKey = 'clean_sheet_fwd';
          if (player.position.toLowerCase().includes('goalkeeper') || player.position.toLowerCase().includes('defender')) {
            actionKey = 'clean_sheet_gk_def';
          } else if (player.position.toLowerCase().includes('midfielder')) {
            actionKey = 'clean_sheet_mid';
          }

          const points = pointsMap.get(actionKey) || 0;
          playerScore += points;
          actions.push({
            action: actionKey,
            points,
            timestamp: new Date(),
          });
        }

        return {
          playerId: player.playerId,
          score: playerScore,
          actions,
        };
      }),
    );

    // Calculate total team score
    const totalScore = playerScores.reduce((sum, player) => sum + player.score, 0);

    // Create or update team score
    let teamScore = await this.userTeamScoreRepository.findOne({
      where: { userTeamId, leagueId },
    });

    if (!teamScore) {
      teamScore = this.userTeamScoreRepository.create({
        userTeamId,
        leagueId,
        totalScore,
        playerScores,
        lastUpdated: new Date(),
      });
    } else {
      teamScore.totalScore = totalScore;
      teamScore.playerScores = playerScores;
      teamScore.lastUpdated = new Date();
    }

    return this.userTeamScoreRepository.save(teamScore);
  }

  /**
   * Retrieves the current score for a user's team in a specific league
   * @param userTeamId - The ID of the user's team
   * @param leagueId - The ID of the league
   * @returns The current team score
   * @throws NotFoundException if the user team is not found
   */
  async getTeamScore(userTeamId: number, leagueId: string): Promise<UserTeamScore> {
    let teamScore = await this.userTeamScoreRepository.findOne({
      where: { userTeamId, leagueId },
    });

    if (!teamScore) {
      // If score doesn't exist, calculate it
      teamScore = await this.calculateTeamScore(userTeamId, leagueId);
    }

    return teamScore;
  }
} 