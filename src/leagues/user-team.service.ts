import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserTeam } from './entities/user-team.entity';
import { User } from '../users/entities/user.entity';
import { League, LeagueType } from './entities/league.entity';
import { PremierLeaguePlayers } from '../premier-league/entities/premier-league-players.entity';
import { CreateUserTeamDto } from './dto/create-user-team.dto';
import { UserTeamScore } from './entities/user-team-score.entity';
import { UpdatePlayerDto } from './dto/update-player.dto';
import { PremierLeagueFixture } from '../premier-league/entities/premier-league-fixture.entity';
import { PremierLeague } from '../premier-league/entities/premier-league.entity';
import { UpdateTeamDto } from './dto/update-team.dto';
import { UserLeagueData } from './entities/user-league-data.entity';

@Injectable()
export class UserTeamService {
  constructor(
    @InjectRepository(UserTeam)
    private readonly userTeamRepository: Repository<UserTeam>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(League)
    private readonly leagueRepository: Repository<League>,
    @InjectRepository(PremierLeaguePlayers)
    private readonly playersRepository: Repository<PremierLeaguePlayers>,
    @InjectRepository(UserTeamScore)
    private readonly userTeamScoreRepository: Repository<UserTeamScore>,
    @InjectRepository(PremierLeagueFixture)
    private readonly fixtureRepository: Repository<PremierLeagueFixture>,
    @InjectRepository(PremierLeague)
    private readonly premierLeagueRepository: Repository<PremierLeague>,
    @InjectRepository(UserLeagueData)
    private readonly userLeagueDataRepository: Repository<UserLeagueData>,
  ) {}

  async create(userId: number, createUserTeamDto: CreateUserTeamDto): Promise<UserTeam> {
    // Check if user exists
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }
    // Check if Premier League exists
    const premierLeague = await this.premierLeagueRepository.findOne({ where: { id: createUserTeamDto.premierLeagueId } });
    if (!premierLeague) {
      throw new NotFoundException('Premier League not found');
    }

    // Check if league exists
    const league = await this.leagueRepository.findOne({ 
      where: { 
        premierLeagueId: createUserTeamDto.premierLeagueId,
        type: LeagueType.Global
      } 
    });
    if (!league) {
      throw new NotFoundException('League not found');
    }

    // Check if user already has a team in this league
    const existingTeam = await this.userTeamRepository.findOne({
      where: {
        user: { id: userId },
        league: { id: createUserTeamDto.leagueId }
      }
    });

    if (existingTeam) {
      throw new BadRequestException('User already has a team in this league');
    }

    // Validate substitute restrictions
    // 1. Check goalkeeper substitutes (exactly one must be true)
    const goalkeeperSubstituteCount = createUserTeamDto.goalkeeperSubstitutes.filter(sub => sub === true).length;
    if (goalkeeperSubstituteCount !== 1) {
      throw new BadRequestException('Exactly one goalkeeper must be marked as substitute');
    }

    // 2. Count total substitutes
    const totalSubstitutes = 
      goalkeeperSubstituteCount +
      createUserTeamDto.defenderSubstitutes.filter(sub => sub === true).length +
      createUserTeamDto.midfielderSubstitutes.filter(sub => sub === true).length +
      createUserTeamDto.forwardSubstitutes.filter(sub => sub === true).length;

    if (totalSubstitutes !== 4) {
      throw new BadRequestException('Total number of substitutes must be exactly 4');
    }

    // Validate player positions
    const goalkeeperPlayers = await this.playersRepository.findByIds(createUserTeamDto.goalkeeperIds);
    const defenderPlayers = await this.playersRepository.findByIds(createUserTeamDto.defenderIds);
    const midfielderPlayers = await this.playersRepository.findByIds(createUserTeamDto.midfielderIds);
    const forwardPlayers = await this.playersRepository.findByIds(createUserTeamDto.forwardIds);

    // Validate goalkeeper positions
    const invalidGoalkeeper = goalkeeperPlayers.find(player => !player.position.toLowerCase().includes('goalkeeper'));
    if (invalidGoalkeeper) {
      throw new BadRequestException(`Invalid goalkeeper selection: ${invalidGoalkeeper.name} is not a goalkeeper`);
    }

    // Validate defender positions
    const invalidDefender = defenderPlayers.find(player => !player.position.toLowerCase().includes('defender'));
    if (invalidDefender) {
      throw new BadRequestException(`Invalid defender selection: ${invalidDefender.name} is not a defender`);
    }

    // Validate midfielder positions
    const invalidMidfielder = midfielderPlayers.find(player => !player.position.toLowerCase().includes('midfielder'));
    if (invalidMidfielder) {
      throw new BadRequestException(`Invalid midfielder selection: ${invalidMidfielder.name} is not a midfielder`);
    }

    // Validate forward positions
    const invalidForward = forwardPlayers.find(player => 
      !player.position.toLowerCase().includes('forward') && 
      !player.position.toLowerCase().includes('attacker')
    );
    if (invalidForward) {
      throw new BadRequestException(`Invalid forward selection: ${invalidForward.name} is not a forward`);
    }

    // Get captain player
    const captain = await this.playersRepository.findOne({ where: { id: createUserTeamDto.captainId } });
    if (!captain) {
      throw new NotFoundException('Captain player not found');
    }

    // Get vice captain player
    const viceCaptain = await this.playersRepository.findOne({ where: { id: createUserTeamDto.viceCaptainId } });
    if (!viceCaptain) {
      throw new NotFoundException('Vice captain player not found');
    }

    // Validate that captain is one of the selected players
    const allPlayers = [...goalkeeperPlayers, ...defenderPlayers, ...midfielderPlayers, ...forwardPlayers];
    const isCaptainInTeam = allPlayers.some(player => player.id === captain.id);
    if (!isCaptainInTeam) {
      throw new BadRequestException('Captain must be one of the selected players');
    }

    // Validate that vice captain is one of the selected players
    const isViceCaptainInTeam = allPlayers.some(player => player.id === viceCaptain.id);
    if (!isViceCaptainInTeam) {
      throw new BadRequestException('Vice captain must be one of the selected players');
    }

    // Validate that captain and vice captain are different players
    if (captain.id === viceCaptain.id) {
      throw new BadRequestException('Captain and vice captain must be different players');
    }

    // Create and save the user team
    const userTeam = this.userTeamRepository.create({
      teamName: createUserTeamDto.teamName,
      formation: createUserTeamDto.formation,
      user,
      league,
      premierLeagueId: createUserTeamDto.premierLeagueId,
      captain,
      viceCaptain,
      goalkeepers: goalkeeperPlayers,
      defenders: defenderPlayers,
      midfielders: midfielderPlayers,
      forwards: forwardPlayers,
      goalkeeperSubstitutes: createUserTeamDto.goalkeeperSubstitutes,
      defenderSubstitutes: createUserTeamDto.defenderSubstitutes,
      midfielderSubstitutes: createUserTeamDto.midfielderSubstitutes,
      forwardSubstitutes: createUserTeamDto.forwardSubstitutes,
    });

    return this.userTeamRepository.save(userTeam);
  }

  async getUserTeam(userId: number, leagueId: number): Promise<UserTeam> {
    // Check if user exists
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if league exists
    const league = await this.leagueRepository.findOne({ where: { premierLeagueId: leagueId } });
    if (!league) {
      throw new NotFoundException('League not found');
    }

    // Find user's team in the specified league
    const userTeam = await this.userTeamRepository
      .createQueryBuilder('userTeam')
      .leftJoinAndSelect('userTeam.user', 'user')
      .leftJoinAndSelect('userTeam.league', 'league')
      .leftJoinAndSelect('userTeam.captain', 'captain')
      .leftJoinAndSelect('userTeam.viceCaptain', 'viceCaptain')
      .leftJoinAndSelect('userTeam.goalkeepers', 'goalkeepers')
      .leftJoinAndSelect('userTeam.defenders', 'defenders')
      .leftJoinAndSelect('userTeam.midfielders', 'midfielders')
      .leftJoinAndSelect('userTeam.forwards', 'forwards')
      .where('userTeam.user.id = :userId', { userId })
      .andWhere('userTeam.premierLeagueId = :leagueId', { leagueId })
      .select([
        'userTeam.id',
        'userTeam.teamName',
        'userTeam.formation',
        'userTeam.createdAt',
        'userTeam.updatedAt',
        'userTeam.goalkeeperSubstitutes',
        'userTeam.defenderSubstitutes',
        'userTeam.midfielderSubstitutes',
        'userTeam.forwardSubstitutes',
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.email',
        'league.id',
        'league.premierLeagueId',
        'league.type',
        'league.status',
        'league.startTime',
        'league.endTime',
        'league.description',
        'captain.id',
        'captain.playerId',
        'captain.name',
        'captain.age',
        'captain.number',
        'captain.position',
        'captain.photo',
        'captain.teamId',
        'viceCaptain.id',
        'viceCaptain.playerId',
        'viceCaptain.name',
        'viceCaptain.age',
        'viceCaptain.number',
        'viceCaptain.position',
        'viceCaptain.photo',
        'viceCaptain.teamId',
        'goalkeepers.id',
        'goalkeepers.playerId',
        'goalkeepers.name',
        'goalkeepers.age',
        'goalkeepers.number',
        'goalkeepers.position',
        'goalkeepers.photo',
        'goalkeepers.teamId',
        'defenders.id',
        'defenders.playerId',
        'defenders.name',
        'defenders.age',
        'defenders.number',
        'defenders.position',
        'defenders.photo',
        'defenders.teamId',
        'midfielders.id',
        'midfielders.playerId',
        'midfielders.name',
        'midfielders.age',
        'midfielders.number',
        'midfielders.position',
        'midfielders.photo',
        'midfielders.teamId',
        'forwards.id',
        'forwards.playerId',
        'forwards.name',
        'forwards.age',
        'forwards.number',
        'forwards.position',
        'forwards.photo',
        'forwards.teamId'
      ])
      .getOne();

    if (!userTeam) {
      throw new NotFoundException('User team not found for this league');
    }

    // Map the substitute status to each player
    if (userTeam.goalkeepers) {
      userTeam.goalkeepers = userTeam.goalkeepers.map((gk, index) => ({
        ...gk,
        isSubstitute: userTeam.goalkeeperSubstitutes[index] || false
      }));
    }

    if (userTeam.defenders) {
      userTeam.defenders = userTeam.defenders.map((def, index) => ({
        ...def,
        isSubstitute: userTeam.defenderSubstitutes[index] || false
      }));
    }

    if (userTeam.midfielders) {
      userTeam.midfielders = userTeam.midfielders.map((mid, index) => ({
        ...mid,
        isSubstitute: userTeam.midfielderSubstitutes[index] || false
      }));
    }

    if (userTeam.forwards) {
      userTeam.forwards = userTeam.forwards.map((fwd, index) => ({
        ...fwd,
        isSubstitute: userTeam.forwardSubstitutes[index] || false
      }));
    }

    // Create a new object without the substitute arrays
    const { 
      goalkeeperSubstitutes, 
      defenderSubstitutes, 
      midfielderSubstitutes, 
      forwardSubstitutes,
      ...userTeamWithoutSubstitutes 
    } = userTeam;

    return userTeamWithoutSubstitutes as UserTeam;
  }

  // async getAllTeamsByLeague(leagueId: string): Promise<UserTeam[]> {
  //   // Check if league exists
  //   const league = await this.leagueRepository.findOne({ where: { id: leagueId } });
  //   if (!league) {
  //     throw new NotFoundException('League not found');
  //   }

  //   // Find all teams in the specified league
  //   const teams = await this.userTeamRepository
  //     .createQueryBuilder('userTeam')
  //     .leftJoinAndSelect('userTeam.user', 'user')
  //     .leftJoinAndSelect('userTeam.league', 'league')
  //     .leftJoinAndSelect('userTeam.captain', 'captain')
  //     .leftJoinAndSelect('userTeam.viceCaptain', 'viceCaptain')
  //     .leftJoinAndSelect('userTeam.goalkeepers', 'goalkeepers')
  //     .leftJoinAndSelect('userTeam.defenders', 'defenders')
  //     .leftJoinAndSelect('userTeam.midfielders', 'midfielders')
  //     .leftJoinAndSelect('userTeam.forwards', 'forwards')
  //     .where('userTeam.league.id = :leagueId', { leagueId })
  //     .select([
  //       'userTeam.id',
  //       'userTeam.teamName',
  //       'userTeam.createdAt',
  //       'userTeam.updatedAt',
  //       'user.id',
  //       'user.firstName',
  //       'user.lastName',
  //       'user.email',
  //       'league.id',
  //       'league.premierLeagueId',
  //       'league.type',
  //       'league.status',
  //       'league.startTime',
  //       'league.endTime',
  //       'league.description',
  //       'captain.id',
  //       'captain.playerId',
  //       'captain.name',
  //       'captain.age',
  //       'captain.number',
  //       'captain.position',
  //       'captain.photo',
  //       'captain.teamId',
  //       'viceCaptain.id',
  //       'viceCaptain.playerId',
  //       'viceCaptain.name',
  //       'viceCaptain.age',
  //       'viceCaptain.number',
  //       'viceCaptain.position',
  //       'viceCaptain.photo',
  //       'viceCaptain.teamId',
  //       'goalkeepers.id',
  //       'goalkeepers.playerId',
  //       'goalkeepers.name',
  //       'goalkeepers.age',
  //       'goalkeepers.number',
  //       'goalkeepers.position',
  //       'goalkeepers.photo',
  //       'goalkeepers.teamId',
  //       'defenders.id',
  //       'defenders.playerId',
  //       'defenders.name',
  //       'defenders.age',
  //       'defenders.number',
  //       'defenders.position',
  //       'defenders.photo',
  //       'defenders.teamId',
  //       'midfielders.id',
  //       'midfielders.playerId',
  //       'midfielders.name',
  //       'midfielders.age',
  //       'midfielders.number',
  //       'midfielders.position',
  //       'midfielders.photo',
  //       'midfielders.teamId',
  //       'forwards.id',
  //       'forwards.playerId',
  //       'forwards.name',
  //       'forwards.age',
  //       'forwards.number',
  //       'forwards.position',
  //       'forwards.photo',
  //       'forwards.teamId'
  //     ])
  //     .getMany();

  //   return teams;
  // }

  async getTeamById(teamId: number): Promise<UserTeam> {
    const userTeam = await this.userTeamRepository
      .createQueryBuilder('userTeam')
      .leftJoinAndSelect('userTeam.user', 'user')
      .leftJoinAndSelect('userTeam.league', 'league')
      .leftJoinAndSelect('userTeam.captain', 'captain')
      .leftJoinAndSelect('userTeam.viceCaptain', 'viceCaptain')
      .leftJoinAndSelect('userTeam.goalkeepers', 'goalkeepers')
      .leftJoinAndSelect('userTeam.defenders', 'defenders')
      .leftJoinAndSelect('userTeam.midfielders', 'midfielders')
      .leftJoinAndSelect('userTeam.forwards', 'forwards')
      .where('userTeam.id = :teamId', { teamId })
      .select([
        'userTeam.id',
        'userTeam.teamName',
        'userTeam.formation',
        'userTeam.createdAt',
        'userTeam.updatedAt',
        'userTeam.goalkeeperSubstitutes',
        'userTeam.defenderSubstitutes',
        'userTeam.midfielderSubstitutes',
        'userTeam.forwardSubstitutes',
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.email',
        'league.id',
        'league.premierLeagueId',
        'league.type',
        'league.status',
        'league.startTime',
        'league.endTime',
        'league.description',
        'captain.id',
        'captain.playerId',
        'captain.name',
        'captain.age',
        'captain.number',
        'captain.position',
        'captain.photo',
        'captain.teamId',
        'viceCaptain.id',
        'viceCaptain.playerId',
        'viceCaptain.name',
        'viceCaptain.age',
        'viceCaptain.number',
        'viceCaptain.position',
        'viceCaptain.photo',
        'viceCaptain.teamId',
        'goalkeepers.id',
        'goalkeepers.playerId',
        'goalkeepers.name',
        'goalkeepers.age',
        'goalkeepers.number',
        'goalkeepers.position',
        'goalkeepers.photo',
        'goalkeepers.teamId',
        'defenders.id',
        'defenders.playerId',
        'defenders.name',
        'defenders.age',
        'defenders.number',
        'defenders.position',
        'defenders.photo',
        'defenders.teamId',
        'midfielders.id',
        'midfielders.playerId',
        'midfielders.name',
        'midfielders.age',
        'midfielders.number',
        'midfielders.position',
        'midfielders.photo',
        'midfielders.teamId',
        'forwards.id',
        'forwards.playerId',
        'forwards.name',
        'forwards.age',
        'forwards.number',
        'forwards.position',
        'forwards.photo',
        'forwards.teamId'
      ])
      .getOne();

    if (!userTeam) {
      throw new NotFoundException('User team not found');
    }

    // Map the substitute status to each player
    if (userTeam.goalkeepers) {
      userTeam.goalkeepers = userTeam.goalkeepers.map((gk, index) => ({
        ...gk,
        isSubstitute: userTeam.goalkeeperSubstitutes[index] || false
      }));
    }

    if (userTeam.defenders) {
      userTeam.defenders = userTeam.defenders.map((def, index) => ({
        ...def,
        isSubstitute: userTeam.defenderSubstitutes[index] || false
      }));
    }

    if (userTeam.midfielders) {
      userTeam.midfielders = userTeam.midfielders.map((mid, index) => ({
        ...mid,
        isSubstitute: userTeam.midfielderSubstitutes[index] || false
      }));
    }

    if (userTeam.forwards) {
      userTeam.forwards = userTeam.forwards.map((fwd, index) => ({
        ...fwd,
        isSubstitute: userTeam.forwardSubstitutes[index] || false
      }));
    }

    // Create a new object without the substitute arrays
    const { 
      goalkeeperSubstitutes, 
      defenderSubstitutes, 
      midfielderSubstitutes, 
      forwardSubstitutes,
      ...userTeamWithoutSubstitutes 
    } = userTeam;

    return userTeamWithoutSubstitutes as UserTeam;
  }

  async getAllTeamsByUser(userId: number): Promise<any[]> {
    // Check if user exists
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Find all teams for the user with minimal fields
    const teams = await this.userTeamRepository
      .createQueryBuilder('userTeam')
      .leftJoinAndSelect('userTeam.league', 'league')
      .leftJoinAndSelect('league.premierLeague', 'premierLeague')
      .leftJoinAndSelect('premierLeague.seasons', 'season')
      .leftJoinAndSelect('userTeam.captain', 'captain')
      .leftJoinAndSelect('userTeam.viceCaptain', 'viceCaptain')
      .leftJoinAndSelect('userTeam.goalkeepers', 'goalkeepers')
      .leftJoinAndSelect('userTeam.defenders', 'defenders')
      .leftJoinAndSelect('userTeam.midfielders', 'midfielders')
      .leftJoinAndSelect('userTeam.forwards', 'forwards')
      .where('userTeam.user.id = :userId', { userId })
      .select([
        'userTeam.id',
        'userTeam.teamName',
        'userTeam.formation',
        'league.id',
        'league.premierLeagueId',
        'league.startTime',
        'league.endTime',
        'premierLeague.name',
        'premierLeague.logo',
        'season.startDate',
        'season.endDate',
        'captain.id',
        'captain.name',
        'captain.photo',
        'viceCaptain.id',
        'viceCaptain.name',
        'viceCaptain.photo',
        'goalkeepers.price',
        'defenders.price',
        'midfielders.price',
        'forwards.price'
      ])
      .getMany();

    // Calculate total price and format response
    return Promise.all(teams.map(async team => {
      const allPlayers = [
        ...team.goalkeepers,
        ...team.defenders,
        ...team.midfielders,
        ...team.forwards
      ];

      const totalPrice = allPlayers.reduce((sum, player) => {
        const price = typeof player.price === 'string' ? parseFloat(player.price) : (player.price || 0);
        return sum + price;
      }, 0);

      // Get the latest gameweek for this team
      const latestGameweek = await this.userTeamScoreRepository
        .createQueryBuilder('score')
        .leftJoin('score.fixture', 'fixture')
        .where('score.userTeamId = :userTeamId', { userTeamId: team.id })
        .andWhere('fixture.round IS NOT NULL')
        .andWhere('fixture.date <= :currentDate', { currentDate: new Date() })
        .andWhere('fixture.date >= :seasonStart', { seasonStart: team.league.startTime })
        .andWhere('fixture.date <= :seasonEnd', { seasonEnd: team.league.endTime })
        .orderBy('fixture.date', 'DESC')
        .select([
          'fixture.round',
          'fixture.fixtureId',
          'fixture.date'
        ])
        .getRawOne();

      return {
        id: team.id,
        teamName: team.teamName,
        league: {
          id: team.league.id,
          premierLeagueId: team.league.premierLeagueId,
          name: team.league.premierLeague.name,
          logo: team.league.premierLeague.logo,
        },
        captain: {
          id: team.captain.id,
          name: team.captain.name,
          photo: team.captain.photo
        },
        viceCaptain: {
          id: team.viceCaptain.id,
          name: team.viceCaptain.name,
          photo: team.viceCaptain.photo
        },
        totalPrice: parseFloat(totalPrice.toFixed(2)),
        latestGameweek: latestGameweek ? {
          round: latestGameweek.fixture_round,
          date: latestGameweek.fixture_date
        } : null
      };
    }));
  }

  async getLeaderboard(leagueId: string): Promise<any[]> {
    // Check if league exists
    const league = await this.leagueRepository.findOne({ where: { id: leagueId } });
    if (!league) {
      throw new NotFoundException('League not found');
    }

    // Get all teams in the league with their scores that have userLeagueData entries
    const teams = await this.userTeamRepository
      .createQueryBuilder('userTeam')
      .leftJoinAndSelect('userTeam.user', 'user')
      .leftJoinAndSelect('userTeam.league', 'league')
      .leftJoinAndSelect('userTeam.captain', 'captain')
      .leftJoinAndSelect('userTeam.viceCaptain', 'viceCaptain')
      .leftJoinAndSelect('userTeam.goalkeepers', 'goalkeepers')
      .leftJoinAndSelect('userTeam.defenders', 'defenders')
      .leftJoinAndSelect('userTeam.midfielders', 'midfielders')
      .leftJoinAndSelect('userTeam.forwards', 'forwards')
      .innerJoin('user_league_data', 'userLeagueData', 'userLeagueData.user_team_id = userTeam.id')
      .where('userTeam.league.id = :leagueId', { leagueId })
      .select([
        'userTeam.id',
        'userTeam.teamName',
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.email',
        'captain.id',
        'captain.name',
        'captain.photo',
        'viceCaptain.id',
        'viceCaptain.name',
        'viceCaptain.photo',
        'goalkeepers.price',
        'defenders.price',
        'midfielders.price',
        'forwards.price'
      ])
      .getMany();

    // Calculate total price and get total score for each team
    const leaderboard = await Promise.all(teams.map(async (team) => {
      if (!team) return null;

      const allPlayers = [
        ...(team.goalkeepers || []),
        ...(team.defenders || []),
        ...(team.midfielders || []),
        ...(team.forwards || [])
      ];

      const totalPrice = allPlayers.reduce((sum, player) => {
        if (!player) return sum;
        const price = typeof player.price === 'string' ? parseFloat(player.price) : (player.price || 0);
        return sum + price;
      }, 0);

      // Get total score from UserTeamScore
      const totalScore = await this.userTeamScoreRepository
        .createQueryBuilder('score')
        .where('score.userTeamId = :userTeamId', { userTeamId: team.id })
        .select('SUM(score.totalScore)', 'total')
        .getRawOne();

      // Get the latest gameweek (round) for this team
      const latestGameweek = await this.userTeamScoreRepository
        .createQueryBuilder('score')
        .leftJoin('score.fixture', 'fixture')
        .where('score.userTeamId = :userTeamId', { userTeamId: team.id })
        .andWhere('fixture.round IS NOT NULL')
        .andWhere('fixture.date <= :currentDate', { currentDate: new Date() })
        .andWhere('fixture.date >= :seasonStart', { seasonStart: new Date('2023-08-11') }) // 2023/24 season start
        .andWhere('fixture.date <= :seasonEnd', { seasonEnd: new Date('2024-05-19') }) // 2023/24 season end
        .orderBy('fixture.date', 'DESC')
        .select([
          'fixture.round',
          'fixture.fixtureId',
          'fixture.date'
        ])
        .getRawOne();

      // Get all scores from the last gameweek
      const lastGameweekScores = latestGameweek ? await this.userTeamScoreRepository
        .createQueryBuilder('score')
        .leftJoin('score.fixture', 'fixture')
        .where('score.userTeamId = :userTeamId', { userTeamId: team.id })
        .andWhere('fixture.round = :round', { round: latestGameweek.fixture_round })
        .andWhere('fixture.date >= :seasonStart', { seasonStart: new Date('2023-08-11') })
        .andWhere('fixture.date <= :seasonEnd', { seasonEnd: new Date('2024-05-19') })
        .orderBy('fixture.date', 'ASC')
        .select([
          'score.fixtureId',
          'score.totalScore',
          'score.playerScores',
          'fixture.round',
          'fixture.date'
        ])
        .getMany() : [];

      // Calculate total points for the last gameweek
      const lastGameweekPoints = lastGameweekScores.reduce((sum, score) => {
        if (!score) return sum;
        return sum + (typeof score.totalScore === 'string' ? parseFloat(score.totalScore) : (score.totalScore || 0));
      }, 0);

      // Get detailed player scores for the last gameweek
      const lastGameweekPlayerScores = lastGameweekScores.map(score => ({
        fixtureId: score.fixtureId,
        round: score.fixture?.round,
        date: score.fixture?.date,
        totalScore: typeof score.totalScore === 'string' ? parseFloat(score.totalScore) : (score.totalScore || 0)
      }));

      return {
        id: team.id,
        teamName: team.teamName,
        user: team.user ? {
          id: team.user.id,
          firstName: team.user.firstName,
          lastName: team.user.lastName,
          email: team.user.email
        } : null,
        captain: team.captain ? {
          id: team.captain.id,
          name: team.captain.name,
          photo: team.captain.photo
        } : null,
        viceCaptain: team.viceCaptain ? {
          id: team.viceCaptain.id,
          name: team.viceCaptain.name,
          photo: team.viceCaptain.photo
        } : null,
        totalPrice: parseFloat(totalPrice.toFixed(2)),
        totalScore: parseFloat(totalScore?.total || '0'),
        lastGameweek: latestGameweek ? {
          round: latestGameweek.fixture_round,
          points: parseFloat(lastGameweekPoints.toFixed(2)),
          fixtures: lastGameweekPlayerScores
        } : {
          round: null,
          points: 0,
          fixtures: []
        }
      };
    }));

    // Filter out null entries and sort by total score in descending order
    return leaderboard
      .filter(entry => entry !== null)
      .sort((a, b) => b.totalScore - a.totalScore);
  }

  /**
   * Returns a list of Premier Leagues the user has participated in, with league name, status, position, and total score.
   */
  async getPremierLeaguesByUser(userId: number): Promise<any[]> {
    // Get all user teams with league and premierLeague info
    const teams = await this.userTeamRepository
      .createQueryBuilder('userTeam')
      .leftJoinAndSelect('userTeam.league', 'league')
      .leftJoinAndSelect('league.premierLeague', 'premierLeague')
      .leftJoinAndSelect('premierLeague.seasons', 'season')
      .where('userTeam.user.id = :userId', { userId })
      .andWhere('league.premierLeagueId IS NOT NULL')
      .select([
        'userTeam.id',
        'league.id',
        'league.startTime',
        'league.endTime',
        'premierLeague.name',
        'season.year',
      ])
      .getMany();

    const now = new Date();
    const result: any[] = [];

    for (const team of teams) {
      // Find the latest season year for this league
      let seasonYear: number | null = null;
      if (team.league.premierLeague && team.league.premierLeague.seasons && team.league.premierLeague.seasons.length > 0) {
        const sortedSeasons = [...team.league.premierLeague.seasons].sort((a, b) => b.year - a.year);
        seasonYear = sortedSeasons[0].year;
      }
      // Get all teams in this league for leaderboard
      const leagueTeams = await this.userTeamRepository
        .createQueryBuilder('userTeam')
        .where('userTeam.league.id = :leagueId', { leagueId: team.league.id })
        .getMany();

      // Get total scores for all teams in this league
      const scores = await Promise.all(
        leagueTeams.map(async t => {
          const totalScore = await this.userTeamScoreRepository
            .createQueryBuilder('score')
            .where('score.userTeamId = :userTeamId', { userTeamId: t.id })
            .select('SUM(score.totalScore)', 'total')
            .getRawOne();
          return {
            teamId: t.id,
            totalScore: parseFloat(totalScore?.total || '0'),
          };
        })
      );
      // Sort by totalScore desc
      scores.sort((a, b) => b.totalScore - a.totalScore);
      // Find user's position
      const userScoreObj = scores.find(s => s.teamId === team.id);
      const position = scores.findIndex(s => s.teamId === team.id) + 1;

      // Calculate status
      let status = 'upcoming';
      if (now < team.league.startTime) status = 'upcoming';
      else if (now > team.league.endTime) status = 'complete';
      else status = 'in progress';

      result.push({
        leagueName: team.league.premierLeague.name,
        status,
        position,
        totalScore: userScoreObj?.totalScore || 0,
        seasonYear,
      });
    }
    return result;
  }

  async updatePlayer(userId: number, teamId: number, updatePlayerDto: UpdatePlayerDto): Promise<UserTeam> {
    // Check if user exists
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Get the user's team with league relationship
    const userTeam = await this.userTeamRepository
      .createQueryBuilder('userTeam')
      .leftJoinAndSelect('userTeam.league', 'league')
      .leftJoinAndSelect('userTeam.goalkeepers', 'goalkeepers')
      .leftJoinAndSelect('userTeam.defenders', 'defenders')
      .leftJoinAndSelect('userTeam.midfielders', 'midfielders')
      .leftJoinAndSelect('userTeam.forwards', 'forwards')
      .where('userTeam.id = :teamId', { teamId })
      .andWhere('userTeam.user.id = :userId', { userId })
      .getOne();

    if (!userTeam) {
      throw new NotFoundException('Team not found');
    }

    if (!userTeam.league) {
      throw new BadRequestException('Team is not associated with any league');
    }

    // Check transfer limit
    // if (userTeam.totalTransfers >= 5) {
    //   throw new BadRequestException('Maximum transfer limit (5) reached for this league');
    // }

    // Get the old and new players
    const oldPlayer = await this.playersRepository.findOne({ where: { id: updatePlayerDto.oldPlayerId } });
    const newPlayer = await this.playersRepository.findOne({ where: { id: updatePlayerDto.newPlayerId } });

    if (!oldPlayer || !newPlayer) {
      throw new NotFoundException('Player not found');
    }

    // Validate player positions match
    if (oldPlayer.position !== newPlayer.position) {
      throw new BadRequestException('New player must play in the same position as the old player');
    }

    // Find which position group the old player belongs to
    let positionGroup: PremierLeaguePlayers[] = [];
    if (userTeam.goalkeepers.some(p => p.id === oldPlayer.id)) {
      positionGroup = userTeam.goalkeepers;
    } else if (userTeam.defenders.some(p => p.id === oldPlayer.id)) {
      positionGroup = userTeam.defenders;
    } else if (userTeam.midfielders.some(p => p.id === oldPlayer.id)) {
      positionGroup = userTeam.midfielders;
    } else if (userTeam.forwards.some(p => p.id === oldPlayer.id)) {
      positionGroup = userTeam.forwards;
    } else {
      throw new BadRequestException('Old player not found in the team');
    }

    // Update the player in the appropriate position group
    const playerIndex = positionGroup.findIndex(p => p.id === oldPlayer.id);
    if (playerIndex === -1) {
      throw new BadRequestException('Old player not found in the team');
    }

    positionGroup[playerIndex] = newPlayer;

    // Update the team
    userTeam.totalTransfers += 1;
    userTeam.lastTransferDate = new Date();

    return this.userTeamRepository.save(userTeam);
  }

  async canUpdatePlayer(teamId: number): Promise<boolean> {
    const userTeam = await this.userTeamRepository.findOne({ where: { id: teamId } });
    
    if (!userTeam) {
      throw new NotFoundException('Team not found');
    }

    if (!userTeam.lastTransferDate) {
      return true;
    }

    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);
    return userTeam.lastTransferDate < tenMinutesAgo;
  }

  async updateTeam(userId: number, teamId: number, updateTeamDto: UpdateTeamDto): Promise<UserTeam> {
    // Check if user exists
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Get the user's team with league relationship
    const userTeam = await this.userTeamRepository
      .createQueryBuilder('userTeam')
      .leftJoinAndSelect('userTeam.user', 'user')
      .leftJoinAndSelect('userTeam.league', 'league')
      .leftJoinAndSelect('userTeam.goalkeepers', 'goalkeepers')
      .leftJoinAndSelect('userTeam.defenders', 'defenders')
      .leftJoinAndSelect('userTeam.midfielders', 'midfielders')
      .leftJoinAndSelect('userTeam.forwards', 'forwards')
      .where('userTeam.id = :teamId', { teamId })
      .andWhere('userTeam.user.id = :userId', { userId })
      .getOne();

    if (!userTeam) {
      throw new NotFoundException('Team not found');
    }

    if (!userTeam.league) {
      throw new BadRequestException('Team is not associated with any league');
    }

    // Check if user has joined any league
    const hasJoined = await this.hasJoinedLeague(teamId);

    // Only check league start time if user has joined a league
    if (hasJoined) {
      const now = new Date();
      if (userTeam.league.startTime && now >= userTeam.league.startTime) {
        throw new BadRequestException('Cannot update team after league has started');
      }
    }

    // Validate substitute restrictions
    // 1. Check goalkeeper substitutes (exactly one must be true)
    const goalkeeperSubstituteCount = updateTeamDto.goalkeeperSubstitutes.filter(sub => sub === true).length;
    if (goalkeeperSubstituteCount !== 1) {
      throw new BadRequestException('Exactly one goalkeeper must be marked as substitute');
    }

    // 2. Count total substitutes
    const totalSubstitutes = 
      goalkeeperSubstituteCount +
      updateTeamDto.defenderSubstitutes.filter(sub => sub === true).length +
      updateTeamDto.midfielderSubstitutes.filter(sub => sub === true).length +
      updateTeamDto.forwardSubstitutes.filter(sub => sub === true).length;

    if (totalSubstitutes !== 4) {
      throw new BadRequestException('Total number of substitutes must be exactly 4');
    }

    // Validate player positions
    const goalkeeperPlayers = await this.playersRepository.findByIds(updateTeamDto.goalkeeperIds);
    const defenderPlayers = await this.playersRepository.findByIds(updateTeamDto.defenderIds);
    const midfielderPlayers = await this.playersRepository.findByIds(updateTeamDto.midfielderIds);
    const forwardPlayers = await this.playersRepository.findByIds(updateTeamDto.forwardIds);

    // Validate goalkeeper positions
    const invalidGoalkeeper = goalkeeperPlayers.find(player => !player.position.toLowerCase().includes('goalkeeper'));
    if (invalidGoalkeeper) {
      throw new BadRequestException(`Invalid goalkeeper selection: ${invalidGoalkeeper.name} is not a goalkeeper`);
    }

    // Validate defender positions
    const invalidDefender = defenderPlayers.find(player => !player.position.toLowerCase().includes('defender'));
    if (invalidDefender) {
      throw new BadRequestException(`Invalid defender selection: ${invalidDefender.name} is not a defender`);
    }

    // Validate midfielder positions
    const invalidMidfielder = midfielderPlayers.find(player => !player.position.toLowerCase().includes('midfielder'));
    if (invalidMidfielder) {
      throw new BadRequestException(`Invalid midfielder selection: ${invalidMidfielder.name} is not a midfielder`);
    }

    // Validate forward positions
    const invalidForward = forwardPlayers.find(player => 
      !player.position.toLowerCase().includes('forward') && 
      !player.position.toLowerCase().includes('attacker')
    );
    if (invalidForward) {
      throw new BadRequestException(`Invalid forward selection: ${invalidForward.name} is not a forward`);
    }

    // Get captain player
    const captain = await this.playersRepository.findOne({ where: { id: updateTeamDto.captainId } });
    if (!captain) {
      throw new NotFoundException('Captain player not found');
    }

    // Get vice captain player
    const viceCaptain = await this.playersRepository.findOne({ where: { id: updateTeamDto.viceCaptainId } });
    if (!viceCaptain) {
      throw new NotFoundException('Vice captain player not found');
    }

    // Validate that captain is one of the selected players
    const allPlayers = [...goalkeeperPlayers, ...defenderPlayers, ...midfielderPlayers, ...forwardPlayers];
    const isCaptainInTeam = allPlayers.some(player => player.id === captain.id);
    if (!isCaptainInTeam) {
      throw new BadRequestException('Captain must be one of the selected players');
    }

    // Validate that vice captain is one of the selected players
    const isViceCaptainInTeam = allPlayers.some(player => player.id === viceCaptain.id);
    if (!isViceCaptainInTeam) {
      throw new BadRequestException('Vice captain must be one of the selected players');
    }

    // Validate that captain and vice captain are different players
    if (captain.id === viceCaptain.id) {
      throw new BadRequestException('Captain and vice captain must be different players');
    }

    // Update the team
    userTeam.teamName = updateTeamDto.teamName;
    userTeam.formation = updateTeamDto.formation;
    userTeam.captain = captain;
    userTeam.viceCaptain = viceCaptain;
    userTeam.goalkeepers = goalkeeperPlayers;
    userTeam.defenders = defenderPlayers;
    userTeam.midfielders = midfielderPlayers;
    userTeam.forwards = forwardPlayers;
    userTeam.goalkeeperSubstitutes = updateTeamDto.goalkeeperSubstitutes;
    userTeam.defenderSubstitutes = updateTeamDto.defenderSubstitutes;
    userTeam.midfielderSubstitutes = updateTeamDto.midfielderSubstitutes;
    userTeam.forwardSubstitutes = updateTeamDto.forwardSubstitutes;

    return this.userTeamRepository.save(userTeam);
  }

  async hasJoinedLeague(userTeamId: number): Promise<boolean> {
    const userLeagueData = await this.userLeagueDataRepository.findOne({
      where: { user_team_id: userTeamId }
    });
    
    return !!userLeagueData;
  }
} 