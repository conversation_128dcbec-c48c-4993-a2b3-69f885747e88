import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserTeam } from './entities/user-team.entity';
import { User } from '../users/entities/user.entity';
import { League } from './entities/league.entity';
import { PremierLeaguePlayers } from '../premier-league/entities/premier-league-players.entity';
import { CreateUserTeamDto } from './dto/create-user-team.dto';

@Injectable()
export class UserTeamService {
  constructor(
    @InjectRepository(UserTeam)
    private readonly userTeamRepository: Repository<UserTeam>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(League)
    private readonly leagueRepository: Repository<League>,
    @InjectRepository(PremierLeaguePlayers)
    private readonly playersRepository: Repository<PremierLeaguePlayers>,
  ) {}

  async create(userId: number, createUserTeamDto: CreateUserTeamDto): Promise<UserTeam> {
    // Check if user exists
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if league exists
    const league = await this.leagueRepository.findOne({ where: { id: createUserTeamDto.leagueId } });
    if (!league) {
      throw new NotFoundException('League not found');
    }

    // Check if user already has a team in this league
    const existingTeam = await this.userTeamRepository.findOne({
      where: {
        user: { id: userId },
        league: { id: createUserTeamDto.leagueId }
      }
    });

    if (existingTeam) {
      throw new BadRequestException('User already has a team in this league');
    }

    // Validate player positions
    const goalkeeperPlayers = await this.playersRepository.findByIds(createUserTeamDto.goalkeeperIds);
    const defenderPlayers = await this.playersRepository.findByIds(createUserTeamDto.defenderIds);
    const midfielderPlayers = await this.playersRepository.findByIds(createUserTeamDto.midfielderIds);
    const forwardPlayers = await this.playersRepository.findByIds(createUserTeamDto.forwardIds);

    // Validate goalkeeper positions
    const invalidGoalkeeper = goalkeeperPlayers.find(player => !player.position.toLowerCase().includes('goalkeeper'));
    if (invalidGoalkeeper) {
      throw new BadRequestException(`Invalid goalkeeper selection: ${invalidGoalkeeper.name} is not a goalkeeper`);
    }

    // Validate defender positions
    const invalidDefender = defenderPlayers.find(player => !player.position.toLowerCase().includes('defender'));
    if (invalidDefender) {
      throw new BadRequestException(`Invalid defender selection: ${invalidDefender.name} is not a defender`);
    }

    // Validate midfielder positions
    const invalidMidfielder = midfielderPlayers.find(player => !player.position.toLowerCase().includes('midfielder'));
    if (invalidMidfielder) {
      throw new BadRequestException(`Invalid midfielder selection: ${invalidMidfielder.name} is not a midfielder`);
    }

    // Validate forward positions
    const invalidForward = forwardPlayers.find(player => 
      !player.position.toLowerCase().includes('forward') && 
      !player.position.toLowerCase().includes('attacker')
    );
    if (invalidForward) {
      throw new BadRequestException(`Invalid forward selection: ${invalidForward.name} is not a forward`);
    }

    // Create and save the user team
    const userTeam = this.userTeamRepository.create({
      teamName: createUserTeamDto.teamName,
      user,
      league,
      goalkeepers: goalkeeperPlayers,
      defenders: defenderPlayers,
      midfielders: midfielderPlayers,
      forwards: forwardPlayers,
    });

    return this.userTeamRepository.save(userTeam);
  }

  async getUserTeam(userId: number, leagueId: string): Promise<UserTeam> {
    // Check if user exists
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException('User not found');
    }

    // Check if league exists
    const league = await this.leagueRepository.findOne({ where: { id: leagueId } });
    if (!league) {
      throw new NotFoundException('League not found');
    }

    // Find user's team in the specified league
    const userTeam = await this.userTeamRepository
      .createQueryBuilder('userTeam')
      .leftJoinAndSelect('userTeam.user', 'user')
      .leftJoinAndSelect('userTeam.league', 'league')
      .leftJoinAndSelect('userTeam.goalkeepers', 'goalkeepers')
      .leftJoinAndSelect('userTeam.defenders', 'defenders')
      .leftJoinAndSelect('userTeam.midfielders', 'midfielders')
      .leftJoinAndSelect('userTeam.forwards', 'forwards')
      .where('userTeam.user.id = :userId', { userId })
      .andWhere('userTeam.league.id = :leagueId', { leagueId })
      .select([
        'userTeam.id',
        'userTeam.teamName',
        'userTeam.createdAt',
        'userTeam.updatedAt',
        'user.id',
        'user.firstName',
        'user.lastName',
        'user.email',
        'league.id',
        'league.premierLeagueId',
        'league.type',
        'league.status',
        'league.startTime',
        'league.endTime',
        'league.description',
        'goalkeepers.id',
        'goalkeepers.playerId',
        'goalkeepers.name',
        'goalkeepers.age',
        'goalkeepers.number',
        'goalkeepers.position',
        'goalkeepers.photo',
        'goalkeepers.teamId',
        'defenders.id',
        'defenders.playerId',
        'defenders.name',
        'defenders.age',
        'defenders.number',
        'defenders.position',
        'defenders.photo',
        'defenders.teamId',
        'midfielders.id',
        'midfielders.playerId',
        'midfielders.name',
        'midfielders.age',
        'midfielders.number',
        'midfielders.position',
        'midfielders.photo',
        'midfielders.teamId',
        'forwards.id',
        'forwards.playerId',
        'forwards.name',
        'forwards.age',
        'forwards.number',
        'forwards.position',
        'forwards.photo',
        'forwards.teamId'
      ])
      .getOne();

    if (!userTeam) {
      throw new NotFoundException('User team not found for this league');
    }

    return userTeam;
  }
} 