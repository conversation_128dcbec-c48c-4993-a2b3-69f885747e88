import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserTeam } from '../entities/user-team.entity';
import { UserTeamScoreService } from '../services/user-team-score.service';

@Injectable()
export class CalculateTeamScoresCron {
  constructor(
    @InjectRepository(UserTeam)
    private readonly userTeamRepository: Repository<UserTeam>,
    private readonly userTeamScoreService: UserTeamScoreService,
  ) {}
  @Cron('* * * * *')
async calculateAllTeamScores() {
    try {
      // Get all user team IDs
      const userTeams = await this.userTeamRepository.find({
        select: ['id'],
      });

      // Calculate scores for each team
      await Promise.all(
        userTeams.map(async (team) => {
          try {
            await this.userTeamScoreService.calculateGlobalTeamScoreByFixture(team.id);
          } catch (error) {
            console.error(`Error calculating score for team ${team.id}:`, error);
          }
        }),
      );

    } catch (error) {
      console.error('Error in calculateAllTeamScores cron job:', error);
    }
  }
} 