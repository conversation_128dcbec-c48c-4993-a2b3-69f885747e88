import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { League, LeagueType, LeagueStatus } from '../entities/league.entity';
import { UserTeam } from '../entities/user-team.entity';
import { UserLeagueData } from '../entities/user-league-data.entity';
import { UserTeamScore } from '../entities/user-team-score.entity';
import { PremierLeagueFixture } from '../../premier-league/entities/premier-league-fixture.entity';
import { In } from 'typeorm';
import { SmartContractService } from '../../solana/services/smart-contract.service';
import { MatchStatus } from '../entities/user-league-data.entity';

@Injectable()
export class OneVsOneLeagueMatchProcessorCron {
  private readonly logger = new Logger(OneVsOneLeagueMatchProcessorCron.name);

  constructor(
    @InjectRepository(League)
    private readonly leagueRepository: Repository<League>,
    @InjectRepository(UserTeam)
    private readonly userTeamRepository: Repository<UserTeam>,
    @InjectRepository(UserLeagueData)
    private readonly userLeagueDataRepository: Repository<UserLeagueData>,
    @InjectRepository(UserTeamScore)
    private readonly userTeamScoreRepository: Repository<UserTeamScore>,
    @InjectRepository(PremierLeagueFixture)
    private readonly premierLeagueFixtureRepository: Repository<PremierLeagueFixture>,
    private readonly smartContractService: SmartContractService,
  ) {}

  // @Cron('* * * * *') // Run every minute
  async processOneVsOneLeagueMatches() {
    try {
      // Get active One vs One leagues with their current week number
      const oneVsOneLeagues = await this.leagueRepository.find({
        where: {
          type: LeagueType.OneVsOne,
        },
        select: ['league_key', 'premierLeagueId', 'week_number']
      });

      if (!oneVsOneLeagues.length) {
        this.logger.debug('No active One vs One leagues found');
        return;
      }

      this.logger.log(`Processing ${oneVsOneLeagues.length} active One vs One leagues`);
      
      // Process each league
      await Promise.all(oneVsOneLeagues.map(async (league) => {
        try {
          // Check if all fixtures for this week are finished
          const lastFixture = await this.premierLeagueFixtureRepository
            .createQueryBuilder('fixture')
            .where('fixture.league_id = :leagueId', { leagueId: league.premierLeagueId })
            .andWhere('fixture.round = :round', { round: `Gameweek ${league.week_number}` })
            .andWhere('fixture.status->>\'short\' = :status', { status: 'FT' })
            .orderBy('fixture.date', 'DESC')
            .getOne();

          if (!lastFixture) {
            this.logger.debug(`League ${league.league_key} - Week ${league.week_number} fixtures are not finished yet`);
            return;
          }

          // Get team IDs and wallet addresses from userLeagueData
          const userLeagueData = await this.userLeagueDataRepository.find({
            where: { league_id: league.league_key },
            select: ['user_team_id', 'user', 'status']
          });

          if (userLeagueData.length !== 2) {
            this.logger.debug(`League ${league.league_key} has ${userLeagueData.length} participants - waiting for exactly 2 participants`);
            return;
          }

          const teamIds = userLeagueData.map(data => data.user_team_id);
          const teamToWalletMap = new Map(userLeagueData.map(data => [data.user_team_id, data.user]));
          
          // Get scores for both teams in a single query
          const teamScores = await this.userTeamScoreRepository
            .createQueryBuilder('score')
            .leftJoinAndSelect('score.userTeam', 'userTeam')
            .leftJoinAndSelect('userTeam.user', 'user')
            .where('score.userTeamId IN (:...teamIds)', { teamIds })
            .andWhere('score.gameWeek = :weekNumber', { weekNumber: league.week_number })
            .select([
              'score.userTeamId',
              'SUM(score.totalScore) as totalscore',
              'userTeam.teamName',
              'user.id',
              'user.firstName',
              'user.lastName'
            ])
            .groupBy('score.userTeamId')
            .addGroupBy('userTeam.teamName')
            .addGroupBy('user.id')
            .addGroupBy('user.firstName')
            .addGroupBy('user.lastName')
            .orderBy('totalscore', 'DESC')
            .getRawMany();

          if (teamScores.length !== 2) {
            this.logger.error(`Expected 2 team scores but got ${teamScores.length} for league ${league.league_key}`);
            return;
          }

          const scoresWithWallets = teamScores.map(score => ({
            ...score,
            wallet: teamToWalletMap.get(score.score_userTeamId)
          }));

          this.logger.log(`Week ${league.week_number} scores with wallets:`, scoresWithWallets);

          // Handle match outcome
          if (scoresWithWallets[0].totalscore !== scoresWithWallets[1].totalscore) {
            await this.handleMatchWithWinner(league, scoresWithWallets);
          } else {
            await this.handleDraw(league, teamIds);
          }
        } catch (error) {
          this.logger.error(`Error processing league ${league.league_key}:`, error);
        }
      }));
    } catch (error) {
      this.logger.error('Error in processOneVsOneLeagueMatches cron job:', error);
    }
  }

  private async handleMatchWithWinner(league: League, scoresWithWallets: any[]) {
    try {
      const result = await this.smartContractService.distributeLeaguePayouts(
        league.league_key,
        scoresWithWallets[0].wallet, // First place (winner)
        scoresWithWallets[1].wallet, // Second place (loser)
        scoresWithWallets[1].wallet  // Third place (also loser)
      );

      if (result.success) {
        this.logger.log(`Successfully distributed payouts for league ${league.league_key}`);
        
        // Update all statuses in a single transaction
        await this.userLeagueDataRepository.manager.transaction(async transactionalEntityManager => {
          // Update winner status
          await transactionalEntityManager.update(
            UserLeagueData,
            { 
              league_id: league.league_key,
              user_team_id: scoresWithWallets[0].score_userTeamId 
            },
            { status: MatchStatus.WIN }
          );

          // Update loser status
          await transactionalEntityManager.update(
            UserLeagueData,
            { 
              league_id: league.league_key,
              user_team_id: scoresWithWallets[1].score_userTeamId 
            },
            { status: MatchStatus.LOSS }
          );

          // Update league status
          await transactionalEntityManager.update(
            League,
            { league_key: league.league_key },
            { status: LeagueStatus.COMPLETED }
          );
        });
      } else {
        this.logger.error(`Failed to distribute payouts for league ${league.league_key}:`, result.error);
      }
    } catch (error) {
      this.logger.error(`Error handling match with winner for league ${league.league_key}:`, error);
    }
  }

  private async handleDraw(league: League, teamIds: number[]) {
    try {
      await this.userLeagueDataRepository.update(
        { 
          league_id: league.league_key,
          user_team_id: In(teamIds) 
        },
        { status: MatchStatus.DRAW }
      );
      this.logger.log(`Match ended in a draw for league ${league.league_key}`);
    } catch (error) {
      this.logger.error(`Error handling draw for league ${league.league_key}:`, error);
    }
  }
} 