import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThanOrEqual, In } from 'typeorm';
import { League, LeagueType, LeagueStatus } from '../entities/league.entity';
import { UserTeam } from '../entities/user-team.entity';
import { UserLeagueData } from '../entities/user-league-data.entity';
import { UserTeamScore } from '../entities/user-team-score.entity';
import { SmartContractService } from '../../solana/services/smart-contract.service';
import { MatchStatus } from '../entities/user-league-data.entity';

@Injectable()
export class GlobalLeagueMatchProcessorCron {
  private readonly logger = new Logger(GlobalLeagueMatchProcessorCron.name);

  constructor(
    @InjectRepository(League)
    private readonly leagueRepository: Repository<League>,
    @InjectRepository(UserTeam)
    private readonly userTeamRepository: Repository<UserTeam>,
    @InjectRepository(UserLeagueData)
    private readonly userLeagueDataRepository: Repository<UserLeagueData>,
    @InjectRepository(UserTeamScore)
    private readonly userTeamScoreRepository: Repository<UserTeamScore>,
    private readonly smartContractService: SmartContractService,
  ) {}

  // @Cron('* * * * *') // Run every minute
  async processGlobalLeagueMatches() {
    try {
      // Get Global leagues that have ended
      const globalLeagues = await this.leagueRepository.find({
        where: {
          type: LeagueType.Global,
          endTime: LessThanOrEqual(new Date()),
        },
        select: ['league_key', 'endTime']
      });

      if (!globalLeagues.length) {
        this.logger.debug('No ended Global leagues found');
        return;
      }

      this.logger.log(`Processing ${globalLeagues.length} ended Global leagues`);
      
      // Process each league
      await Promise.all(globalLeagues.map(async (league) => {
        try {
          // Get all teams and their wallet addresses from userLeagueData
          const userLeagueData = await this.userLeagueDataRepository.find({
            where: { league_id: league.league_key },
            select: ['user_team_id', 'user', 'status']
          });

          if (userLeagueData.length < 3) {
            this.logger.debug(`League ${league.league_key} has ${userLeagueData.length} participants - need at least 3 participants`);
            return;
          }

          const teamIds = userLeagueData.map(data => data.user_team_id);
          const teamToWalletMap = new Map(userLeagueData.map(data => [data.user_team_id, data.user]));
          
          // Get scores for all teams in a single query
          const teamScores = await this.userTeamScoreRepository
            .createQueryBuilder('score')
            .leftJoinAndSelect('score.userTeam', 'userTeam')
            .leftJoinAndSelect('userTeam.user', 'user')
            .where('score.userTeamId IN (:...teamIds)', { teamIds })
            .select([
              'score.userTeamId',
              'SUM(score.totalScore) as totalscore',
              'userTeam.teamName',
              'user.id',
              'user.firstName',
              'user.lastName'
            ])
            .groupBy('score.userTeamId')
            .addGroupBy('userTeam.teamName')
            .addGroupBy('user.id')
            .addGroupBy('user.firstName')
            .addGroupBy('user.lastName')
            .orderBy('totalscore', 'DESC')
            .getRawMany();

          if (teamScores.length < 3) {
            this.logger.error(`Expected at least 3 team scores but got ${teamScores.length} for league ${league.league_key}`);
            return;
          }

          const scoresWithWallets = teamScores.map(score => ({
            ...score,
            wallet: teamToWalletMap.get(score.score_userTeamId)
          }));

          this.logger.log(`Global league scores with wallets:`, scoresWithWallets);

          // Get top 3 teams
          const [firstPlace, secondPlace, thirdPlace] = scoresWithWallets;

          // Distribute payouts
          try {
            const result = await this.smartContractService.distributeLeaguePayouts(
              league.league_key,
              firstPlace.wallet,  // First place
              secondPlace.wallet, // Second place
              thirdPlace.wallet   // Third place
            );

            if (result.success) {
              this.logger.log(`Successfully distributed payouts for league ${league.league_key}`);
              
              // Update all statuses in a single transaction
              await this.userLeagueDataRepository.manager.transaction(async transactionalEntityManager => {
                // Update first place status
                await transactionalEntityManager.update(
                  UserLeagueData,
                  { 
                    league_id: league.league_key,
                    user_team_id: firstPlace.score_userTeamId 
                  },
                  { status: MatchStatus.WIN }
                );

                // Update second place status
                await transactionalEntityManager.update(
                  UserLeagueData,
                  { 
                    league_id: league.league_key,
                    user_team_id: secondPlace.score_userTeamId 
                  },
                  { status: MatchStatus.WIN }
                );

                // Update third place status
                await transactionalEntityManager.update(
                  UserLeagueData,
                  { 
                    league_id: league.league_key,
                    user_team_id: thirdPlace.score_userTeamId 
                  },
                  { status: MatchStatus.WIN }
                );

                // Update remaining teams status
                await transactionalEntityManager.update(
                  UserLeagueData,
                  { 
                    league_id: league.league_key,
                    user_team_id: In(teamIds.filter(id => 
                      id !== firstPlace.score_userTeamId && 
                      id !== secondPlace.score_userTeamId && 
                      id !== thirdPlace.score_userTeamId
                    ))
                  },
                  { status: MatchStatus.LOSS }
                );

                // Update league status
                await transactionalEntityManager.update(
                  League,
                  { league_key: league.league_key },
                  { status: LeagueStatus.COMPLETED }
                );
              });
            } else {
              this.logger.error(`Failed to distribute payouts for league ${league.league_key}:`, result.error);
            }
          } catch (error) {
            this.logger.error(`Error distributing payouts for league ${league.league_key}:`, error);
          }
        } catch (error) {
          this.logger.error(`Error processing league ${league.league_key}:`, error);
        }
      }));
    } catch (error) {
      this.logger.error('Error in processGlobalLeagueMatches cron job:', error);
    }
  }
} 