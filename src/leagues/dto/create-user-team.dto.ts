import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsArray, ArrayMinSize, ArrayMaxSize } from 'class-validator';

export class CreateUserTeamDto {
  @ApiProperty({
    description: 'Name of the team',
    example: 'Dream Team FC',
  })
  @IsString()
  teamName: string;

  @ApiProperty({
    description: 'ID of the league this team belongs to',
    example: 'league-123',
  })
  @IsString()
  leagueId: string;

  @ApiProperty({
    description: 'Array of goalkeeper player IDs (1-2 required)',
    example: [1, 2],
    type: [Number],
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(2)
  goalkeeperIds: number[];

  @ApiProperty({
    description: 'Array of defender player IDs (1-5 required)',
    example: [2, 3, 4, 5, 6],
    type: [Number],
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(5)
  defenderIds: number[];

  @ApiProperty({
    description: 'Array of midfielder player IDs (1-5 required)',
    example: [6, 7, 8, 9, 10],
    type: [Number],
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(5)
  midfielderIds: number[];

  @ApiProperty({
    description: 'Array of forward player IDs (1-4 required)',
    example: [11, 12, 13, 14],
    type: [Number],
  })
  @IsArray()
  @ArrayMinSize(1)
  @ArrayMaxSize(4)
  forwardIds: number[];
} 