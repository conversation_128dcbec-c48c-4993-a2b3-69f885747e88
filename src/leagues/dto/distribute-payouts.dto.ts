import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class DistributePayoutsDto {
  @ApiProperty({
    description: 'Public key of the first place winner',
    example: '7v91N7iZ9mNicL8WfG6cgSCKyRXydQjLh6UYBWwm6y1Q',
  })
  @IsString()
  firstPlace: string;

  @ApiProperty({
    description: 'Public key of the second place winner',
    example: '7v91N7iZ9mNicL8WfG6cgSCKyRXydQjLh6UYBWwm6y1Q',
  })
  @IsString()
  secondPlace: string;

  @ApiProperty({
    description: 'Public key of the third place winner',
    example: '7v91N7iZ9mNicL8WfG6cgSCKyRXydQjLh6UYBWwm6y1Q',
  })
  @IsString()
  thirdPlace: string;
} 