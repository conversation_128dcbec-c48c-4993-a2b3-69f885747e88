import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsArray, ArrayMinSize, ArrayMaxSize, IsBoolean } from 'class-validator';

export class UpdateTeamDto {
  @ApiProperty({
    description: 'Name of the team',
    example: 'Dream Team FC',
  })
  @IsString()
  teamName: string;

  @ApiProperty({
    description: 'Formation of the team (e.g., "4-4-2", "3-5-2")',
    example: '4-4-2',
  })
  @IsString()
  formation: string;

  @ApiProperty({
    description: 'ID of the player selected as team captain',
    example: 1,
  })
  @IsNumber()
  captainId: number;

  @ApiProperty({
    description: 'ID of the player selected as team vice captain',
    example: 2,
  })
  @IsNumber()
  viceCaptainId: number;

  @ApiProperty({
    description: 'Array of goalkeeper player IDs (exactly 2 required)',
    example: [1, 2],
    type: [Number],
  })
  @IsArray()
  @ArrayMinSize(2)
  @ArrayMaxSize(2)
  goalkeeperIds: number[];

  @ApiProperty({
    description: 'Array of boolean values indicating if each goalkeeper is a substitute',
    example: [false, true],
    type: [Boolean],
  })
  @IsArray()
  @ArrayMinSize(2)
  @ArrayMaxSize(2)
  goalkeeperSubstitutes: boolean[];

  @ApiProperty({
    description: 'Array of defender player IDs (1-5 required)',
    example: [2, 3, 4, 5, 6],
    type: [Number],
  })
  @IsArray()
  @ArrayMinSize(5)
  @ArrayMaxSize(5)
  defenderIds: number[];

  @ApiProperty({
    description: 'Array of boolean values indicating if each defender is a substitute',
    example: [false, false, true, false, false],
    type: [Boolean],
  })
  @IsArray()
  @ArrayMinSize(5)
  @ArrayMaxSize(5)
  defenderSubstitutes: boolean[];

  @ApiProperty({
    description: 'Array of midfielder player IDs (1-5 required)',
    example: [6, 7, 8, 9, 10],
    type: [Number],
  })
  @IsArray()
  @ArrayMinSize(5)
  @ArrayMaxSize(5)
  midfielderIds: number[];

  @ApiProperty({
    description: 'Array of boolean values indicating if each midfielder is a substitute',
    example: [false, true, false, false, false],
    type: [Boolean],
  })
  @IsArray()
  @ArrayMinSize(5)
  @ArrayMaxSize(5)
  midfielderSubstitutes: boolean[];

  @ApiProperty({
    description: 'Array of forward player IDs (1-4 required)',
    example: [11, 12, 13, 14],
    type: [Number],
  })
  @IsArray()
  @ArrayMinSize(3)
  @ArrayMaxSize(3)
  forwardIds: number[];

  @ApiProperty({
    description: 'Array of boolean values indicating if each forward is a substitute',
    example: [false, false, true],
    type: [Boolean],
  })
  @IsArray()
  @ArrayMinSize(3)
  @ArrayMaxSize(3)
  forwardSubstitutes: boolean[];
} 