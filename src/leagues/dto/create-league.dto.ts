import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, IsInt } from 'class-validator';

export class CreateLeagueDto {
  @ApiProperty({
    description: 'Entry fee for the league in SOL',
    example: 0.1,
  })
  @IsNumber()
  @Min(0)
  entryFee: number;

  @ApiProperty({
    description: 'Maximum number of participants allowed in the league',
    example: 100,
  })
  @IsInt()
  @Min(2)
  participantLimit: number;

  @ApiProperty({
    description: 'Start time of the league in Unix timestamp',
    example: 1682323200,
  })
  @IsNumber()
  startTime: number;

  @ApiProperty({
    description: 'End time of the league in Unix timestamp',
    example: 1682409600,
  })
  @IsNumber()
  startEnd: number;

  @ApiProperty({
    description: 'Percentage of prize pool for first place',
    example: 50,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  firstPlacePercentage: number;

  @ApiProperty({
    description: 'Percentage of prize pool for second place',
    example: 30,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  secondPlacePercentage: number;

  @ApiProperty({
    description: 'Percentage of prize pool for third place',
    example: 20,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  thirdPlacePercentage: number;

  @ApiProperty({
    description: 'Description of the league',
    example: 'Weekly Fantasy League - Week 1',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Premier League ID',
    example: 1,
  })
  @IsNumber()
  premierLeagueId: number;

  @ApiProperty({
    description: 'sesson year',
    example: 1,
  })
  @IsNumber()
  sesson: number;
} 
