import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON><PERSON>, IsN<PERSON>ber, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class ScoreUpdateDto {
  @ApiProperty({
    description: 'Public key of the user',
    example: '8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x8x',
  })
  @IsString()
  user: string;

  @ApiProperty({
    description: 'New score for the user',
    example: 100,
  })
  @IsNumber()
  score: number;
}

export class UpdateLeagueScoresDto {
  @ApiProperty({
    description: 'Array of score updates',
    type: [ScoreUpdateDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ScoreUpdateDto)
  scoreUpdates: ScoreUpdateDto[];
} 