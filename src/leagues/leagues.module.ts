import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { League } from './entities/league.entity';
import { LeaguesController } from './leagues.controller';
import { SolanaModule } from '../solana/solana.module';
import { UserLeagueData } from './entities/user-league-data.entity';
import { User } from '../users/entities/user.entity';
import { UserTeam } from './entities/user-team.entity';
import { UserTeamController } from './user-team.controller';
import { UserTeamService } from './user-team.service';
import { PremierLeaguePlayers } from '../premier-league/entities/premier-league-players.entity';
import { UserTeamScore } from './entities/user-team-score.entity';
import { UserTeamScoreService } from './services/user-team-score.service';
import { UserTeamScoreController } from './controllers/user-team-score.controller';
import { PointsConfig } from '../points-config/entities/points-config.entity';
import { PremierLeaguePlayerStats } from '../premier-league/entities/premier-league-player-stats.entity';
import { WalletModule } from '../wallet/wallet.module';
import { Wallet } from '../wallet/entities/wallet.entity';
import { PremierLeagueFixture } from '../premier-league/entities/premier-league-fixture.entity';
import { PremierLeague } from '../premier-league/entities/premier-league.entity';
import { PremierLeagueModule } from '../premier-league/premier-league.module';
import { CalculateTeamScoresCron } from './cron/calculate-team-scores.cron';
import { OneVsOneLeagueMatchProcessorCron } from './cron/process-1vs1-league-matches.cron';
import { GlobalLeagueMatchProcessorCron } from './cron/global-league-match-processor.cron';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      League,
      UserLeagueData,
      User,
      UserTeam,
      PremierLeaguePlayers,
      UserTeamScore,
      PointsConfig,
      PremierLeaguePlayerStats,
      Wallet,
      PremierLeagueFixture,
      PremierLeague,
    ]),
    ScheduleModule.forRoot(),
    SolanaModule,
    WalletModule,
    PremierLeagueModule,
  ],
  controllers: [LeaguesController, UserTeamController, UserTeamScoreController],
  providers: [UserTeamService, UserTeamScoreService, CalculateTeamScoresCron, OneVsOneLeagueMatchProcessorCron,GlobalLeagueMatchProcessorCron],
  exports: [TypeOrmModule],
})
export class LeaguesModule {} 