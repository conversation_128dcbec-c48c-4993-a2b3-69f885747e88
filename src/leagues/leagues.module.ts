import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { League } from './entities/league.entity';
import { LeaguesController } from './leagues.controller';
import { SolanaModule } from '../solana/solana.module';
import { UserLeagueData } from './entities/user-league-data.entity';
import { User } from '../users/entities/user.entity';
import { UserTeam } from './entities/user-team.entity';
import { UserTeamController } from './user-team.controller';
import { UserTeamService } from './user-team.service';
import { PremierLeaguePlayers } from '../premier-league/entities/premier-league-players.entity';
import { UserTeamScore } from './entities/user-team-score.entity';
import { UserTeamScoreService } from './services/user-team-score.service';
import { UserTeamScoreController } from './controllers/user-team-score.controller';
import { PointsConfig } from '../config/points-config.entity';
import { PremierLeaguePlayerStats } from '../premier-league/entities/premier-league-player-stats.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      League,
      UserLeagueData,
      User,
      UserTeam,
      PremierLeaguePlayers,
      UserTeamScore,
      PointsConfig,
      PremierLeaguePlayerStats,
    ]),
    SolanaModule
  ],
  controllers: [LeaguesController, UserTeamController, UserTeamScoreController],
  providers: [UserTeamService, UserTeamScoreService],
  exports: [TypeOrmModule],
})
export class LeaguesModule {} 