import { Entity, PrimaryGeneratedColumn, Column, ManyToOne, CreateDateColumn, UpdateDateColumn, JoinColumn } from 'typeorm';
import { UserTeam } from './user-team.entity';
import { League } from './league.entity';
import { ApiProperty } from '@nestjs/swagger';
import { PremierLeagueFixture } from '../../premier-league/entities/premier-league-fixture.entity';

@Entity('user_team_scores')
export class UserTeamScore {
  @ApiProperty({ description: 'Unique identifier for the team score' })
  @PrimaryGeneratedColumn()
  id: number;

  @ApiProperty({ description: 'ID of the user team' })
  @Column()
  userTeamId: number;

  @ApiProperty({ type: () => UserTeam, description: 'Associated user team' })
  @ManyToOne(() => UserTeam, userTeam => userTeam.id)
  @JoinColumn({ name: 'userTeamId' })
  userTeam: UserTeam;

  // @ApiProperty({ description: 'ID of the league' })
  // @Column()
  // leagueId: string;

  // @ApiProperty({ type: () => League, description: 'Associated league' })
  // @ManyToOne(() => League, league => league.id)
  // @JoinColumn({ name: 'leagueId' })
  // league: League;

  @ApiProperty({ description: 'ID of the fixture' })
  @Column({ nullable: true })
  fixtureId: number;

  @ApiProperty({ type: () => PremierLeagueFixture, description: 'Associated fixture' })
  @ManyToOne(() => PremierLeagueFixture)
  @JoinColumn({ name: 'fixtureId', referencedColumnName: 'fixtureId' })
  fixture: PremierLeagueFixture;

  @ApiProperty({ description: 'Game week number' })
  @Column({ nullable: true })
  gameWeek: number;

  @ApiProperty({ description: 'Total score of the team', type: 'number', format: 'float' })
  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  totalScore: number;

  @ApiProperty({
    description: 'Detailed scores for each player',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        playerId: { type: 'number', description: 'ID of the player' },
        score: { type: 'number', description: 'Total score for the player' },
        actions: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              action: { type: 'string', description: 'Type of action (e.g., goal, assist)' },
              points: { type: 'number', description: 'Points earned for this action' },
              timestamp: { type: 'string', format: 'date-time', description: 'When the action occurred' }
            }
          }
        }
      }
    }
  })
  @Column({ type: 'jsonb', nullable: true })
  playerScores: {
    playerId: number;
    score: number;
    actions: {
      action: string;
      points: number;
      timestamp: Date;
    }[];
  }[];

  @ApiProperty({ description: 'Last time the score was updated', type: 'string', format: 'date-time' })
  @Column({ type: 'timestamp' })
  lastUpdated: Date;

  @ApiProperty({ description: 'When the score record was created', type: 'string', format: 'date-time' })
  @CreateDateColumn()
  createdAt: Date;

  @ApiProperty({ description: 'When the score record was last updated', type: 'string', format: 'date-time' })
  @UpdateDateColumn()
  updatedAt: Date;

  @Column({ type: 'varchar', length: 255, unique: true })
  uniqueScoreKey: string;
} 