import { <PERSON>tity, PrimaryGeneratedColumn, Column, ManyToOne, ManyToMany, JoinTable, CreateDateColumn, UpdateDateColumn, OneToMany, JoinColumn } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { League } from './league.entity';
import { PremierLeaguePlayers } from '../../premier-league/entities/premier-league-players.entity';
import { ValidateNested, ArrayMinSize, ArrayMaxSize } from 'class-validator';
import { Type } from 'class-transformer';
import { UserLeagueData } from './user-league-data.entity';

@Entity('user_teams')
export class UserTeam {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  teamName: string;

  @Column({ type: 'varchar', length: 10 ,nullable: true})
  formation: string;

  @ManyToOne(() => User, user => user.id, { eager: true })
  user: User;

  @ManyToOne(() => League, league => league.id, { eager: true })
  league: League;

  @Column({ type: 'int' ,nullable: true })
  premierLeagueId: number;

  @OneToMany(() => UserLeagueData, userLeagueData => userLeagueData.userTeam)
  userLeagueData: UserLeagueData[];

  @ManyToOne(() => PremierLeaguePlayers)
  @JoinColumn()
  captain: PremierLeaguePlayers;

  @ManyToOne(() => PremierLeaguePlayers)
  @JoinColumn()
  viceCaptain: PremierLeaguePlayers;

  // Goalkeeper - 1-2 players
  @ManyToMany(() => PremierLeaguePlayers)
  @JoinTable()
  @ValidateNested({ each: true })
  @Type(() => PremierLeaguePlayers)
  @ArrayMinSize(1)
  @ArrayMaxSize(2)
  goalkeepers: PremierLeaguePlayers[];

  @Column({ type: 'boolean', array: true, default: [false, false] })
  goalkeeperSubstitutes: boolean[];

  // Defenders - 1-5 players
  @ManyToMany(() => PremierLeaguePlayers)
  @JoinTable()
  @ValidateNested({ each: true })
  @Type(() => PremierLeaguePlayers)
  @ArrayMinSize(1)
  @ArrayMaxSize(5)
  defenders: PremierLeaguePlayers[];

  @Column({ type: 'boolean', array: true, default: [false, false, false, false, false] })
  defenderSubstitutes: boolean[];

  // Midfielders - 1-5 players
  @ManyToMany(() => PremierLeaguePlayers)
  @JoinTable()
  @ValidateNested({ each: true })
  @Type(() => PremierLeaguePlayers)
  @ArrayMinSize(1)
  @ArrayMaxSize(5)
  midfielders: PremierLeaguePlayers[];

  @Column({ type: 'boolean', array: true, default: [false, false, false, false, false] })
  midfielderSubstitutes: boolean[];

  // Forwards - 1-4 players
  @ManyToMany(() => PremierLeaguePlayers)
  @JoinTable()
  @ValidateNested({ each: true })
  @Type(() => PremierLeaguePlayers)
  @ArrayMinSize(1)
  @ArrayMaxSize(4)
  forwards: PremierLeaguePlayers[];

  @Column({ type: 'boolean', array: true, default: [false, false, false, false] })
  forwardSubstitutes: boolean[];

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  totalPrice: number;

  @Column({ type: 'int', default: 0 })
  totalTransfers: number;

  @Column({ type: 'timestamp', nullable: true })
  lastTransferDate: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 