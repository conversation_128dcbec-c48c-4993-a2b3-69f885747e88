import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, ManyToMany, JoinTable, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { User } from '../../users/entities/user.entity';
import { League } from './league.entity';
import { PremierLeaguePlayers } from '../../premier-league/entities/premier-league-players.entity';
import { ValidateNested, ArrayMinSize, ArrayMaxSize } from 'class-validator';
import { Type } from 'class-transformer';
import { UserLeagueData } from './user-league-data.entity';

@Entity('user_teams')
export class UserTeam {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'varchar', length: 255 })
  teamName: string;

  @ManyToOne(() => User, user => user.id, { eager: true })
  user: User;

  @ManyToOne(() => League, league => league.id, { eager: true })
  league: League;

  @OneToMany(() => UserLeagueData, userLeagueData => userLeagueData.userTeam)
  userLeagueData: UserLeagueData[];

  // Goalkeeper - 1-2 players
  @ManyToMany(() => PremierLeaguePlayers)
  @JoinTable()
  @ValidateNested({ each: true })
  @Type(() => PremierLeaguePlayers)
  @ArrayMinSize(1)
  @ArrayMaxSize(2)
  goalkeepers: PremierLeaguePlayers[];

  // Defenders - 1-5 players
  @ManyToMany(() => PremierLeaguePlayers)
  @JoinTable()
  @ValidateNested({ each: true })
  @Type(() => PremierLeaguePlayers)
  @ArrayMinSize(1)
  @ArrayMaxSize(5)
  defenders: PremierLeaguePlayers[];

  // Midfielders - 1-5 players
  @ManyToMany(() => PremierLeaguePlayers)
  @JoinTable()
  @ValidateNested({ each: true })
  @Type(() => PremierLeaguePlayers)
  @ArrayMinSize(1)
  @ArrayMaxSize(5)
  midfielders: PremierLeaguePlayers[];

  // Forwards - 1-4 players
  @ManyToMany(() => PremierLeaguePlayers)
  @JoinTable()
  @ValidateNested({ each: true })
  @Type(() => PremierLeaguePlayers)
  @ArrayMinSize(1)
  @ArrayMaxSize(4)
  forwards: PremierLeaguePlayers[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 