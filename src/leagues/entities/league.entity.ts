import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany, ManyToOne, JoinColumn } from 'typeorm';
import { UserLeagueData } from './user-league-data.entity';
import { PremierLeague } from '../../premier-league/entities/premier-league.entity';

export enum LeagueStatus {
  REGISTRATION = 'REGISTRATION',
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED'
}

export enum LeagueType {
  OneVsOne = 'OneVsOne',
  Global = 'Global',
}

@Entity('leagues')
export class League {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true, type: 'int' })
  premierLeagueId: number;

  @ManyToOne(() => PremierLeague)
  @JoinColumn({ name: 'premierLeagueId', referencedColumnName: 'id' })
  premierLeague: PremierLeague;

  @Column()
  creator: string;

  @Column({ unique: true })
  league_key: string;

  @OneToMany(() => UserLeagueData, userLeagueData => userLeagueData.league)
  userLeagueData: UserLeagueData[];

  @Column({ type: 'decimal', precision: 20, scale: 2 })
  entryFee: number;

  @Column()
  participantLimit: number;

  @Column({ type: 'timestamp' })
  startTime: Date;

  @Column({ type: 'timestamp' })
  endTime: Date;

  @Column({
    type: 'enum',
    enum: LeagueStatus,
    default: LeagueStatus.REGISTRATION
  })
  status: LeagueStatus;

  @Column()
  firstPlacePercentage: number;

  @Column()
  secondPlacePercentage: number;

  @Column()
  thirdPlacePercentage: number;

  @Column({ default: 0 })
  currentParticipants: number;

  @Column({ type: 'decimal', precision: 19, scale: 2, default: 0 })
  totalPrizePool: number;

  @Column({ type: 'text' })
  description: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({
    type: 'enum',
    enum: LeagueType,
    default: LeagueType.Global
  })
  type: LeagueType;

  @Column({nullable: true})
  sesson: number;

  @Column({ type: 'text',nullable: true })
  globle_league_key: string;

  @Column({nullable: true})
  week_number: number;
} 