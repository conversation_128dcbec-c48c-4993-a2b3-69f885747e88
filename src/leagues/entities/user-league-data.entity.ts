import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { League } from './league.entity';
import { UserTeam } from './user-team.entity';
import { User } from '../../users/entities/user.entity';

@Entity('user_league_data')
export class UserLeagueData {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: false })
  user: string;

  @ManyToOne(() => User)
  @JoinColumn({ name: 'user', referencedColumnName: 'walletAddress' })
  userData: User;

  @Column()
  league_id: string;

  @ManyToOne(() => League, league => league.userLeagueData)
  @JoinColumn({ name: 'league_id', referencedColumnName: 'league_key' })
  league: League;

  @Column()
  user_team_id: number;

  @ManyToOne(() => UserTeam, userTeam => userTeam.id)
  @JoinColumn({ name: 'user_team_id' })
  userTeam: UserTeam;

  @Column({ type: 'bytea' })
  team_selection_hash: Buffer;

  @Column({ type: 'timestamp' })
  last_updated: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
} 