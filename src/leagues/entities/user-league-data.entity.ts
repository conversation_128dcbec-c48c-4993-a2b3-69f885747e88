import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { League } from './league.entity';
import { UserTeam } from './user-team.entity';
import { Wallet } from '../../wallet/entities/wallet.entity';

export enum MatchStatus {
  WIN = 'win',
  LOSS = 'loss',
  DRAW = 'draw',
  PENDING = 'pending'
}

@Entity('user_league_data')
export class UserLeagueData {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: false })
  user: string;

  @ManyToOne(() => Wallet)
  @JoinColumn({ name: 'user', referencedColumnName: 'address' })
  wallet: Wallet;

  @Column()
  league_id: string;

  @ManyToOne(() => League, league => league.userLeagueData)
  @JoinColumn({ name: 'league_id', referencedColumnName: 'league_key' })
  league: League;

  @Column()
  user_team_id: number;

  @ManyToOne(() => UserTeam, userTeam => userTeam.id)
  @JoinColumn({ name: 'user_team_id' })
  userTeam: UserTeam;

  @Column({ type: 'bytea' })
  team_selection_hash: Buffer;

  @Column({ type: 'timestamp' })
  last_updated: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @Column({
    type: 'enum',
    enum: MatchStatus,
    default: MatchStatus.PENDING,
    nullable: true
  })
  status: MatchStatus;
} 