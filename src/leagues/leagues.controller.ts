import { Controller, Post, Body, UseGuards, Param, Get, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../auth/guards/admin.guard';
import { LeagueService } from '../solana/league.service';
import { CreateLeagueDto } from './dto/create-league.dto';
import { UpdateLeagueScoresDto } from './dto/update-league-scores.dto';
import { DistributePayoutsDto } from './dto/distribute-payouts.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { League } from './entities/league.entity';
import { UserLeagueData } from './entities/user-league-data.entity';
import { User } from '../users/entities/user.entity';
import { UUID } from 'crypto';

@ApiTags('Leagues')
@Controller('leagues')
export class LeaguesController {
  constructor(
    private readonly leagueService: LeagueService,
    @InjectRepository(League)
    private readonly leagueRepository: Repository<League>,
    @InjectRepository(UserLeagueData)
    private readonly userLeagueDataRepository: Repository<UserLeagueData>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all leagues with user participation data' })
  @ApiQuery({ name: 'ID', required: false, description: 'Filter by league ID' })
  @ApiResponse({
    status: 200,
    description: 'Returns all leagues with their user participation data',
  })
  async getAllLeagues(@Query('ID') ID?: UUID) {
    const queryBuilder = this.leagueRepository.createQueryBuilder('league')
      .leftJoinAndSelect('league.userLeagueData', 'userLeagueData')
      .orderBy('league.createdAt', 'DESC');

    if (ID) {
      queryBuilder.where('league.ID = :ID', { ID });
    }

    const leagues = await queryBuilder.getMany();

    // Get all unique user public keys from userLeagueData
    const userPublicKeys = leagues.flatMap(league => 
      league.userLeagueData.map(data => data.user)
    ).filter((value, index, self) => self.indexOf(value) === index);

    // Fetch all users with matching wallet addresses
    const users = await this.userRepository.find({
      where: userPublicKeys.map(walletAddress => ({ walletAddress })),
      select: ['id', 'firstName', 'lastName', 'walletAddress'],
    });

    // Create a map of wallet address to user info
    const userMap = new Map(
      users.map(user => [user.walletAddress, user])
    );

    return leagues.map(league => ({
      ...league,
      userLeagueData: league.userLeagueData.map(data => {
        const userInfo = userMap.get(data.user);
        return {
          user: data.user,
          userTeamId: data.user_team_id,
          userTeam: data.userTeam,
          lastUpdated: data.last_updated,
          createdAt: data.createdAt,
          userInfo: userInfo ? {
            id: userInfo.id,
            firstName: userInfo.firstName,
            lastName: userInfo.lastName,
          } : null,
        };
      }),
    }));
  }

  @Post()
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Create a new league (Admin only)' })
  @ApiResponse({
    status: 201,
    description: 'The league has been successfully created.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required.' })
  async createLeague(@Body() createLeagueDto: CreateLeagueDto) {
    return this.leagueService.createLeague(
      createLeagueDto.entryFee,
      createLeagueDto.participantLimit,
      createLeagueDto.startTime,
      createLeagueDto.startEnd,
      createLeagueDto.firstPlacePercentage,
      createLeagueDto.secondPlacePercentage,
      createLeagueDto.thirdPlacePercentage,
      createLeagueDto.description,
      createLeagueDto.premierLeagueId
    );
  }

  // @Post(':leagueKey/scores')
  // @UseGuards(JwtAuthGuard, AdminGuard)
  // @ApiBearerAuth('JWT-auth')
  // @ApiOperation({ summary: 'Update league scores (Admin only)' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'The league scores have been successfully updated.',
  // })
  // @ApiResponse({ status: 401, description: 'Unauthorized.' })
  // @ApiResponse({ status: 403, description: 'Forbidden - Admin access required.' })
  // async updateLeagueScores(
  //   @Param('leagueKey') leagueKey: string,
  //   @Body() updateLeagueScoresDto: UpdateLeagueScoresDto
  // ) {
  //   return this.leagueService.updateLeagueScores(leagueKey, updateLeagueScoresDto.scoreUpdates);
  // }

  @Post(':leagueKey/distribute-payouts')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Distribute league payouts (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'The league payouts have been successfully distributed.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required.' })
  async distributePayouts(
    @Param('leagueKey') leagueKey: string,
    @Body() distributePayoutsDto: DistributePayoutsDto
  ) {
    return this.leagueService.distributeLeaguePayouts(
      leagueKey,
      distributePayoutsDto.firstPlace,
      distributePayoutsDto.secondPlace,
      distributePayoutsDto.thirdPlace
    );
  }

  
} 