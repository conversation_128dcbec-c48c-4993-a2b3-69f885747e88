import {
  Controller,
  Post,
  Body,
  UseGuards,
  Param,
  Get,
  Query,
  NotFoundException,
  BadRequestException,
  Request,
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags, ApiQuery } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AdminGuard } from '../auth/guards/admin.guard';
import { SmartContractService } from '../solana/services/smart-contract.service';
import { CreateLeagueDto } from './dto/create-league.dto';
import { UpdateLeagueScoresDto } from './dto/update-league-scores.dto';
import { DistributePayoutsDto } from './dto/distribute-payouts.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { League } from './entities/league.entity';
import { UserLeagueData } from './entities/user-league-data.entity';
import { User } from '../users/entities/user.entity';
import { Wallet } from '../users/entities/wallet.entity';
import { ConfigService } from '@nestjs/config';
import { LeagueType } from './entities/league.entity';

enum LeagueFilter {
  MY_MATCHES = 'MyMatches',
  JOINED_MATCHES = 'JoinedMatches',
  OTHERS_MATCHES = 'OthersMatches',
}

@ApiTags('Leagues')
@Controller('leagues')
@ApiBearerAuth('JWT-auth')
export class LeaguesController {
  constructor(
    private readonly smartContractService: SmartContractService,
    @InjectRepository(League)
    private readonly leagueRepository: Repository<League>,
    @InjectRepository(UserLeagueData)
    private readonly userLeagueDataRepository: Repository<UserLeagueData>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Wallet)
    private readonly walletRepository: Repository<Wallet>,
    private readonly configService: ConfigService,
  ) {}

  @Get('all')
  @ApiOperation({ summary: 'Get all leagues with user participation data' })
  @ApiQuery({
    name: 'type',
    enum: LeagueType,
    required: false,
    description: 'Filter leagues by type (OneVsOne or Global)',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns all leagues with their user participation data',
  })
  async getAllLeagues(@Query('type') type?: LeagueType) {
    const queryBuilder = this.leagueRepository
      .createQueryBuilder('league')
      .leftJoinAndSelect('league.premierLeague', 'premierLeague')
      .orderBy('league.createdAt', 'DESC');

    if (type) {
      queryBuilder.where('league.type = :type', { type });
    }

    const leagues = await queryBuilder.getMany();

    return leagues.map((league) => {
      const { premierLeague, ...leagueData } = league;
      return {
        ...leagueData,
        leagueLogo: premierLeague?.logo || null,
        countryName: premierLeague?.countryName || null,
        countryCode: premierLeague?.countryCode || null,
        countryFlag: premierLeague?.countryFlag || null,
      };
    });
  }

  @Get()
  @ApiOperation({ summary: 'Get a specific league by ID or creator' })
  @ApiQuery({
    name: 'id',
    required: false,
    description: 'Optional league ID to find specific league',
  })
  @ApiQuery({
    name: 'creator',
    required: false,
    description: 'Optional creator address to find leagues by creator',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns the league with the specified ID or creator',
  })
  @ApiResponse({
    status: 400,
    description: 'Either id or creator parameter is required',
  })
  @ApiResponse({
    status: 404,
    description: 'League not found',
  })
  async getLeagueById(@Query('id') id?: string, @Query('creator') creator?: string) {
    if (!id && !creator) {
      throw new BadRequestException('Either id or creator parameter is required');
    }

    const queryBuilder = this.leagueRepository
      .createQueryBuilder('league')
      .leftJoinAndSelect('league.premierLeague', 'premierLeague');

    if (id) {
      queryBuilder.where('league.id = :id', { id });
    }

    if (creator) {
      queryBuilder.where('league.creator = :creator', { creator });
    }

    const league = await queryBuilder.getOne();

    if (!league) {
      throw new NotFoundException(`League not found`);
    }

    const { premierLeague, ...leagueData } = league;

    // Get main league information only if globle_league_key is not null
    let mainLeagueInfo: { mainLeagueId: string; mainLeagueStartTime: Date; mainLeagueEndTime: Date } | null =
      null;
    if (league.globle_league_key) {
      const mainLeague = await this.leagueRepository.findOne({
        where: { league_key: league.globle_league_key },
        select: ['id', 'startTime', 'endTime'],
      });

      if (mainLeague) {
        mainLeagueInfo = {
          mainLeagueId: mainLeague.id,
          mainLeagueStartTime: mainLeague.startTime,
          mainLeagueEndTime: mainLeague.endTime,
        };
      }
    }

    // Calculate total game weeks
    const startDate = new Date(league.startTime);
    const endDate = new Date(league.endTime);
    const totalGameWeeks = Math.ceil((endDate.getTime() - startDate.getTime()) / (7 * 24 * 60 * 60 * 1000));

    return {
      ...leagueData,
      ...(mainLeagueInfo || {}),
      leagueLogo: premierLeague?.logo || null,
      countryName: premierLeague?.countryName || null,
      countryCode: premierLeague?.countryCode || null,
      countryFlag: premierLeague?.countryFlag || null,
      contractAddress: this.configService.get<string>('SOLANA_PROGRAM_ID'),
      totalGameWeeks,
    };
  }

  @Post()
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Create a new league (Admin only)' })
  @ApiResponse({
    status: 201,
    description: 'The league has been successfully created.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required.' })
  async createLeague(@Body() createLeagueDto: CreateLeagueDto) {
    return this.smartContractService.createLeague(
      createLeagueDto.entryFee,
      createLeagueDto.participantLimit,
      createLeagueDto.startTime,
      createLeagueDto.startEnd,
      createLeagueDto.firstPlacePercentage,
      createLeagueDto.secondPlacePercentage,
      createLeagueDto.thirdPlacePercentage,
      createLeagueDto.description,
      createLeagueDto.premierLeagueId,
      createLeagueDto.sesson,
    );
  }

  @Post(':leagueKey/distribute-payouts')
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiBearerAuth('JWT-auth')
  @ApiOperation({ summary: 'Distribute league payouts (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'The league payouts have been successfully distributed.',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required.' })
  async distributePayouts(
    @Param('leagueKey') leagueKey: string,
    @Body() distributePayoutsDto: DistributePayoutsDto,
  ) {
    return this.smartContractService.distributeLeaguePayouts(
      leagueKey,
      distributePayoutsDto.firstPlace,
      distributePayoutsDto.secondPlace,
      distributePayoutsDto.thirdPlace,
    );
  }

  @Get(':id/prize-distribution')
  @ApiOperation({ summary: 'Get prize distribution information for a league' })
  @ApiResponse({
    status: 200,
    description: 'Returns the prize distribution information for the specified league',
  })
  @ApiResponse({
    status: 404,
    description: 'League not found',
  })
  async getPrizeDistribution(@Param('id') id: string) {
    const league = await this.leagueRepository.findOne({ where: { id } });
    if (!league) {
      throw new NotFoundException(`League with ID ${id} not found`);
    }

    // Convert lamports to SOL (1 SOL = 1,000,000,000 lamports)
    const LAMPORTS_PER_SOL = 1000000000;
    const totalPrizePoolInSol = Number(league.totalPrizePool) / LAMPORTS_PER_SOL;

    const firstPlacePrize = (totalPrizePoolInSol * league.firstPlacePercentage) / 100;
    const secondPlacePrize = (totalPrizePoolInSol * league.secondPlacePercentage) / 100;
    const thirdPlacePrize = (totalPrizePoolInSol * league.thirdPlacePercentage) / 100;

    return {
      totalPrizePoolInSol,
      firstPlacePrize,
      secondPlacePrize,
      thirdPlacePrize,
      firstPlacePercentage: league.firstPlacePercentage,
      secondPlacePercentage: league.secondPlacePercentage,
      thirdPlacePercentage: league.thirdPlacePercentage,
    };
  }

  @Get(':id/participants')
  @ApiOperation({ summary: 'Get all participant users data for a specific league' })
  @ApiResponse({
    status: 200,
    description: 'Returns all participant users data for the specified league',
  })
  @ApiResponse({
    status: 404,
    description: 'League not found',
  })
  async getLeagueParticipants(@Param('id') id: string) {
    const league = await this.leagueRepository
      .createQueryBuilder('league')
      .leftJoinAndSelect('league.userLeagueData', 'userLeagueData')
      .leftJoinAndSelect('userLeagueData.wallet', 'wallet')
      .leftJoinAndSelect('wallet.user', 'user')
      .where('league.id = :id', { id })
      .getOne();

    if (!league) {
      throw new NotFoundException(`League with ID ${id} not found`);
    }

    const currentParticipants = league.userLeagueData.length;
    const remainingSpots = league.participantLimit - currentParticipants;

    return {
      participantLimit: league.participantLimit,
      currentParticipants,
      remainingSpots,
      participants: league.userLeagueData.map((data) => {
        const userInfo = data.wallet?.user;
        return {
          id: userInfo.id,
          firstName: userInfo.firstName,
          lastName: userInfo.lastName,
          userWalletAddress: data.user,
          userTeamId: data.user_team_id,
          userTeam: data.userTeam,
          lastUpdated: data.last_updated,
          createdAt: data.createdAt,
        };
      }),
    };
  }

  @Get('check-participation')
  @ApiOperation({
    summary:
      'Check if a team has participated in a specific league and return their league data if they have',
  })
  @ApiQuery({
    name: 'league_key',
    required: true,
    description: 'The league key to check participation in',
  })
  @ApiQuery({
    name: 'team',
    required: true,
    description: 'The team ID to check participation for',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'Returns whether the team has participated in the league and their league data if they have',
  })
  @ApiResponse({
    status: 404,
    description: 'League not found',
  })
  async checkTeamParticipation(@Query('league_key') leagueKey: string, @Query('team') team: number) {
    const league = await this.leagueRepository.findOne({ where: { league_key: leagueKey } });
    if (!league) {
      throw new NotFoundException(`League with key ${leagueKey} not found`);
    }

    const userLeagueData = await this.userLeagueDataRepository.findOne({
      where: {
        league: { league_key: leagueKey },
        user_team_id: team,
      },
      relations: ['wallet', 'wallet.user', 'league'],
    });

    if (!userLeagueData) {
      return {
        hasParticipated: false,
        leagueKey,
        teamId: team,
      };
    }

    const userInfo = userLeagueData.wallet?.user;
    return {
      hasParticipated: true,
      leagueKey,
      teamId: team,
      userLeagueData: {
        id: userLeagueData.id,
        user_team_id: userLeagueData.user_team_id,
        userTeam: userLeagueData.userTeam,
        last_updated: userLeagueData.last_updated,
        createdAt: userLeagueData.createdAt,
        updatedAt: userLeagueData.updatedAt,
        userInfo: userInfo
          ? {
              id: userInfo.id,
              firstName: userInfo.firstName,
              lastName: userInfo.lastName,
              email: userInfo.email,
              walletAddress: userLeagueData.wallet?.address,
            }
          : null,
      },
    };
  }

  @Get('user-leagues')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get leagues where user is creator or participant' })
  @ApiQuery({
    name: 'filter',
    enum: LeagueFilter,
    required: true,
    description: 'Filter leagues by user relationship (MyMatches, JoinedMatches, OthersMatches)',
  })
  @ApiQuery({
    name: 'type',
    enum: LeagueType,
    required: false,
    description: 'Filter leagues by type (OneVsOne or Global)',
  })
  @ApiResponse({
    status: 200,
    description: 'Returns leagues where user is creator or participant',
  })
  async getUserLeagues(
    @Request() req,
    @Query('filter') filter: LeagueFilter,
    @Query('type') type?: LeagueType,
  ) {
    const userId = req.user.id;

    // Get all user's wallet addresses
    const userWallets = await this.walletRepository.find({
      where: { user: { id: userId } },
    });

    if (!userWallets.length) {
      return [];
    }

    const walletAddresses = userWallets.map((wallet) => wallet.address);

    let leagues: League[] = [];

    switch (filter) {
      case LeagueFilter.MY_MATCHES:
        leagues = await this.getCreatedLeagues(walletAddresses, type);
        break;
      case LeagueFilter.JOINED_MATCHES:
        leagues = await this.getParticipatedLeagues(walletAddresses, type);
        break;
      case LeagueFilter.OTHERS_MATCHES:
        leagues = await this.getNotCreatedLeagues(walletAddresses, type);
        break;
    }

    // Sort by participant count
    leagues.sort((a, b) => a.currentParticipants - b.currentParticipants);

    return leagues.map((league) => {
      const { premierLeague, userLeagueData, ...leagueData } = league;

      // Check if user has joined by looking at userLeagueData
      const hasJoined = userLeagueData?.some((data) => walletAddresses.includes(data.user)) || false;

      // Find the global league ID from the queried results
      // const leagueInfo = leagueIds.find(info => info.leagueId === league.id);
      // const globalLeagueId = leagueInfo?.globalLeagueId || null;

      return {
        // leagueId: globalLeagueId,
        ...leagueData,
        leagueLogo: premierLeague?.logo || null,
        countryName: premierLeague?.countryName || null,
        countryCode: premierLeague?.countryCode || null,
        countryFlag: premierLeague?.countryFlag || null,
        currentParticipants: league.currentParticipants,
        hasJoined,
      };
    });
  }

  private async getCreatedLeagues(walletAddresses: string[], type?: LeagueType): Promise<League[]> {
    const query = this.leagueRepository
      .createQueryBuilder('league')
      .leftJoinAndSelect('league.premierLeague', 'premierLeague')
      .leftJoinAndSelect('league.userLeagueData', 'userLeagueData')
      .where('league.creator IN (:...walletAddresses)', { walletAddresses })
      .andWhere('league.currentParticipants = 0');

    if (type) {
      query.andWhere('league.type = :type', { type });
    }

    return query.getMany();
  }

  private async getParticipatedLeagues(walletAddresses: string[], type?: LeagueType): Promise<League[]> {
    const query = this.leagueRepository
      .createQueryBuilder('league')
      .leftJoinAndSelect('league.premierLeague', 'premierLeague')
      .leftJoinAndSelect('league.userLeagueData', 'userLeagueData')
      .where('userLeagueData.user IN (:...walletAddresses)', { walletAddresses });

    if (type) {
      query.andWhere('league.type = :type', { type });
    }

    return query.getMany();
  }

  private async getNotCreatedLeagues(walletAddresses: string[], type?: LeagueType): Promise<League[]> {
    const query = this.leagueRepository
      .createQueryBuilder('league')
      .leftJoinAndSelect('league.premierLeague', 'premierLeague')
      .leftJoinAndSelect('league.userLeagueData', 'userLeagueData')
      .where('league.creator NOT IN (:...walletAddresses)', { walletAddresses })
      .andWhere('league.currentParticipants > 0');

    if (type) {
      query.andWhere('league.type = :type', { type });
    }

    return query.getMany();
  }
}
