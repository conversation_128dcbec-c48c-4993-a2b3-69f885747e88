# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
*.tsbuildinfo

# Coverage reports
coverage/
*.lcov

# Test files
**/*.spec.ts
**/*.test.ts
test/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# TypeScript declaration files
*.d.ts

# Package manager files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Docker files
Dockerfile
docker-compose.yml
.dockerignore

# Git files
.git/
.gitignore

# Documentation
README.md
CHANGELOG.md
LICENSE
