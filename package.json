{"name": "leauge_manager_back", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "STAGE=dev nest start --watch", "start:qa": "STAGE=qa nest start --watch", "start:uat": "STAGE=uat nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "STAGE=prod node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typedoc": "typedoc --options ./typedoc.json", "typeorm": "cross-env STAGE=dev ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js -d src/configs/ormconfig.ts", "migration:create": "npx typeorm migration:create ./src/database/migrations/Migration", "migration:generate": "npm run typeorm -- migration:generate ./src/database/migrations/Migration -p", "migration:run": "npm run typeorm -- migration:run", "migration:revert": "npm run typeorm -- migration:revert"}, "dependencies": {"@aws-sdk/client-s3": "^3.828.0", "@aws-sdk/s3-request-presigner": "^3.828.0", "@coral-xyz/anchor": "^0.30.1", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.1.4", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^6.4.0", "@nestjs/typeorm": "^11.0.0", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.98.1", "@types/multer": "^1.4.13", "argon2": "^0.43.0", "axios": "^1.9.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.8.0", "connect-pg-simple": "^10.0.0", "cookie-parser": "^1.4.7", "cross-env": "^7.0.3", "csurf": "^1.11.0", "express-session": "^1.18.1", "helmet": "^7.1.0", "hpp": "^0.2.3", "multer": "^2.0.1", "nest-winston": "^1.10.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.14.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.22", "winston": "^3.17.0", "xss-clean": "^0.1.4"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s", "!**/*.spec.ts", "!**/*.interface.ts", "!**/main.ts", "!**/index.ts"], "coverageDirectory": "../coverage", "coverageReporters": ["text", "lcov", "html", "json"], "testEnvironment": "node", "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}}}